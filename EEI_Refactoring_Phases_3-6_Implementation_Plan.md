# EEI Analysis Tool Refactoring: Phases 3-6 Implementation Plan

## Project Status Overview

**Current State (After Phases 1-2):**
- Original File Size: 4,942 lines
- Current File Size: 4,887 lines
- Lines Reduced: 55 lines
- Major Duplications Eliminated: ~356 lines of CPEI/PEIL code consolidated
- **Target**: Reduce to 2,500-3,000 lines (40-50% reduction)
- **Remaining Reduction Needed**: ~1,887-2,387 lines

**Completed:**
- ✅ Phase 1: Foundation Classes (SafeFormatter, DataValidator, Configuration)
- ✅ Phase 2: Unified Impedance Analysis Classes (ImpedanceCalculator, ImpedancePlotter)

---

## Phase 3: Analysis Workflows Consolidation
**Priority: HIGH | Estimated Reduction: 600-800 lines | Timeline: 3-4 days**

### 3.1 Objective
Consolidate individual and merged well analysis workflows into a unified `WellAnalyzer` class, eliminating repetitive well data preparation, validation, and analysis execution patterns.

### 3.2 Target Functions and Line Ranges

#### Primary Targets:
1. **`individual_well_analysis`** (lines ~2429-2800) - ~371 lines
2. **`merged_well_analysis`** (lines ~2801-3307) - ~506 lines
3. **Well preparation patterns** scattered throughout analysis functions

#### Secondary Targets:
- Repetitive well data validation logic
- Common depth range handling
- Analysis method selection patterns
- Result formatting and collection

### 3.3 Implementation Plan

#### 3.3.1 WellAnalyzer Class Structure
```python
class WellAnalyzer:
    """Unified well analysis handler for individual and merged analysis"""

    def __init__(self, las_files, log_keywords, analysis_config):
        self.las_files = las_files
        self.log_keywords = log_keywords
        self.config = analysis_config
        self.alternative_mnemonics = {}
        self.required_base_logs = ['DEPTH', 'DT', 'DTS', 'RHOB']

    def analyze(self, target_log, depth_ranges, analysis_mode='individual'):
        """Main analysis entry point"""
        if analysis_mode == 'individual':
            return self._analyze_individual_wells(target_log, depth_ranges)
        else:
            return self._analyze_merged_wells(target_log, depth_ranges)

    def _prepare_well_data(self, las, well_name, target_log):
        """Common well data preparation and validation"""
        current_well_columns = find_default_columns(las, self.log_keywords)

        # Validate base logs
        base_mnemonics = {}
        missing_logs = []

        for log_name in self.required_base_logs:
            mnemonic = current_well_columns.get(log_name)
            if mnemonic is None or mnemonic not in las.curves:
                missing_logs.append(f"{log_name} (searched: {self.log_keywords.get(log_name, [])})")
            else:
                base_mnemonics[log_name] = mnemonic

        if missing_logs:
            return {'valid': False, 'reason': 'missing_base_logs', 'missing': missing_logs}

        # Handle target log
        target_mnemonic = self._resolve_target_log(las, well_name, target_log, current_well_columns)
        if target_mnemonic is None:
            return {'valid': False, 'reason': 'missing_target_log'}

        # Optional VCL
        vcl_mnemonic = current_well_columns.get('VCL')

        return {
            'valid': True,
            'base_mnemonics': base_mnemonics,
            'target_mnemonic': target_mnemonic,
            'vcl_mnemonic': vcl_mnemonic
        }

    def _perform_well_analysis(self, las, well_data, well_name, target_log, top_depth, bottom_depth):
        """Perform analysis based on configured method"""
        if self.config['analysis_method'] == 1:  # EEI
            calculator = ImpedanceCalculator('EEI')
            return self._analyze_eei(calculator, las, well_data, well_name, target_log, top_depth, bottom_depth)
        elif self.config['analysis_method'] == 2:  # CPEI
            calculator = ImpedanceCalculator('CPEI')
            return self._analyze_cpei_peil(calculator, las, well_data, well_name, target_log, top_depth, bottom_depth, 'CPEI')
        elif self.config['analysis_method'] == 3:  # PEIL
            calculator = ImpedanceCalculator('PEIL')
            return self._analyze_cpei_peil(calculator, las, well_data, well_name, target_log, top_depth, bottom_depth, 'PEIL')

    def _analyze_individual_wells(self, target_log, depth_ranges):
        """Individual well analysis with consolidated logic"""
        all_wells_results = []
        all_wells_data = []

        for las in self.las_files:
            well_name = las.well.WELL.value
            top_depth, bottom_depth = depth_ranges[well_name]

            # Common well preparation
            well_data = self._prepare_well_data(las, well_name, target_log)
            if not well_data['valid']:
                all_wells_results.append(self._create_empty_result(well_name, top_depth, bottom_depth))
                all_wells_data.append(self._create_empty_data(well_name))
                continue

            # Perform analysis
            result = self._perform_well_analysis(las, well_data, well_name, target_log, top_depth, bottom_depth)
            all_wells_results.append(result['summary'])
            all_wells_data.append(result['data'])

        return all_wells_results, all_wells_data
```

#### 3.3.2 Before/After Pattern Example

**Before (Repetitive Pattern - ~50 lines each occurrence):**
```python
# In individual_well_analysis
current_well_columns = find_default_columns(las, log_keywords)
base_mnemonics = {}
missing_logs = []

for log_name in ['DEPTH', 'DT', 'DTS', 'RHOB']:
    mnemonic = current_well_columns.get(log_name)
    if mnemonic is None or mnemonic not in las.curves:
        missing_logs.append(f"{log_name}")
    else:
        base_mnemonics[log_name] = mnemonic

# Similar pattern repeated in merged_well_analysis with slight variations
```

**After (Unified Pattern - ~15 lines):**
```python
# In WellAnalyzer._prepare_well_data
well_data = self._prepare_well_data(las, well_name, target_log)
if not well_data['valid']:
    return self._create_empty_result(well_name, top_depth, bottom_depth)
```

### 3.4 Implementation Steps

1. **Create WellAnalyzer class skeleton** (Day 1)
2. **Extract common well preparation logic** (Day 1-2)
3. **Consolidate individual well analysis** (Day 2)
4. **Consolidate merged well analysis** (Day 3)
5. **Update function calls throughout codebase** (Day 3-4)
6. **Testing and validation** (Day 4)

### 3.5 Risk Assessment
- **Medium Risk**: Complex interdependencies between analysis functions
- **Mitigation**: Implement incremental changes with thorough testing
- **Fallback**: Maintain legacy function wrappers during transition

---

## Phase 4: Plotting Functions Consolidation
**Priority: MEDIUM | Estimated Reduction: 300-400 lines | Timeline: 2-3 days**

### 4.1 Objective
Consolidate repetitive matplotlib setup, formatting, and plotting patterns into a unified `PlotGenerator` class.

### 4.2 Target Functions and Line Ranges

#### Primary Targets:
1. **`plot_eei_vs_target`** (lines ~2000-2427) - ~427 lines
2. **Correlation heatmap plotting** (scattered in CPEI/PEIL functions)
3. **Summary chart generation** (lines ~4750-4800)

#### Repetitive Patterns:
- Matplotlib figure setup and configuration
- Axis scaling and limit calculation
- Color mapping and annotation
- Legend and label formatting
- Subplot arrangement

### 4.3 Implementation Plan

#### 4.3.1 PlotGenerator Class Structure
```python
class PlotGenerator:
    """Unified plotting utilities for all analysis types"""

    @staticmethod
    def plot_correlation_heatmap(correlation_matrix, n_values, phi_values,
                               optimal_n, optimal_phi, analysis_type,
                               target_log, well_name=None, **kwargs):
        """Generic heatmap plotting for CPEI/PEIL"""
        plt.figure(figsize=(12, 8))
        plt.imshow(correlation_matrix, aspect='auto', origin='lower', cmap='viridis')
        plt.colorbar(label='Correlation Coefficient')
        plt.xlabel('Phi (degrees)')
        plt.ylabel('n (exponent)')

        title = f'{analysis_type}-{target_log} Correlation Matrix'
        if well_name:
            title += f' for Well: {well_name}'
            if 'top_depth' in kwargs and 'bottom_depth' in kwargs:
                title += f'\nDepth range: {kwargs["top_depth"]:.2f} - {kwargs["bottom_depth"]:.2f}'
        else:
            title += ' for Merged Wells'

        plt.title(title)

        # Set tick labels
        phi_ticks = range(0, len(phi_values), 30)
        n_ticks = range(0, len(n_values), 5)
        plt.xticks(phi_ticks, [phi_values[i] for i in phi_ticks])
        plt.yticks(n_ticks, [f"{n_values[i]:.1f}" for i in n_ticks])

        # Mark optimal point
        optimal_phi_idx = list(phi_values).index(optimal_phi)
        optimal_n_idx = list(n_values).index(optimal_n)

        plt.plot(optimal_phi_idx, optimal_n_idx, 'r*', markersize=15,
                label=f'Optimal: n={optimal_n:.1f}, φ={optimal_phi}°')

        # Add annotation
        max_correlation = np.nanmax(correlation_matrix)
        plt.annotate(f'Max Correlation: {max_correlation:.4f}',
                    xy=(optimal_phi_idx, optimal_n_idx),
                    xytext=(optimal_phi_idx + 10, optimal_n_idx + 2),
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.2'),
                    fontsize=10, fontweight='bold')

        PlotGenerator._add_summary_textbox(analysis_type, well_name, optimal_n,
                                         optimal_phi, max_correlation, target_log)

        plt.legend()
        plt.tight_layout()
        plt.show()

    @staticmethod
    def plot_well_comparison(all_wells_data, target_log, depth_ranges, analysis_type):
        """Unified plotting for individual well comparisons"""
        global_min, global_max = PlotGenerator._calculate_global_percentiles(all_wells_data)

        # Get user input for axis ranges
        x_min, x_max = PlotGenerator._get_user_axis_range(target_log)

        for well_data in all_wells_data:
            if not PlotGenerator._validate_well_data(well_data):
                continue

            PlotGenerator._plot_single_well(well_data, target_log, depth_ranges,
                                          analysis_type, global_min, global_max,
                                          x_min, x_max)

    @staticmethod
    def _add_summary_textbox(analysis_type, well_name, optimal_n, optimal_phi,
                           max_correlation, target_log):
        """Add summary text box to plots"""
        summary_text = f'{analysis_type} '
        summary_text += 'Individual Well Summary:\n' if well_name else 'Merged Analysis Summary:\n'

        if well_name:
            summary_text += f'Well: {well_name}\n'

        summary_text += f'Optimal n: {optimal_n:.1f}\n'
        summary_text += f'Optimal φ: {optimal_phi:.0f}°\n'
        summary_text += f'Max Correlation: {max_correlation:.4f}\n'
        summary_text += f'Target: {target_log}'

        color = 'lightblue' if analysis_type == 'CPEI' else 'lightgreen'
        plt.text(0.02, 0.98, summary_text, transform=plt.gca().transAxes,
                bbox=dict(boxstyle="round,pad=0.5", facecolor=color, alpha=0.8),
                verticalalignment='top', fontsize=9, fontfamily='monospace')

    @staticmethod
    def _calculate_global_percentiles(all_wells_data):
        """Calculate global percentiles for consistent axis scaling"""
        all_impedance = []
        for well_data in all_wells_data:
            if well_data.get('normalized_eei') is not None:
                all_impedance.extend(well_data['normalized_eei'])

        if all_impedance:
            return np.nanpercentile(all_impedance, [2, 98])
        return 0, 1
```

#### 4.3.2 Before/After Pattern Example

**Before (Repetitive matplotlib setup - ~30 lines each):**
```python
plt.figure(figsize=(12, 8))
plt.imshow(correlation_matrix, aspect='auto', origin='lower', cmap='viridis')
plt.colorbar(label='Correlation Coefficient')
plt.xlabel('Phi (degrees)')
plt.ylabel('n (exponent)')
# ... 25 more lines of similar setup
```

**After (Unified call - ~3 lines):**
```python
PlotGenerator.plot_correlation_heatmap(
    correlation_matrix, n_values, phi_values, optimal_n, optimal_phi, 'CPEI'
)
```

### 4.4 Implementation Steps

1. **Identify common plotting patterns** (Day 1)
2. **Create PlotGenerator class with core methods** (Day 1-2)
3. **Consolidate heatmap plotting** (Day 2)
4. **Consolidate well comparison plotting** (Day 2-3)
5. **Update all plotting function calls** (Day 3)
6. **Testing and visual validation** (Day 3)

---

## Phase 5: Dialog Creation Simplification
**Priority: LOW | Estimated Reduction: 200-300 lines | Timeline: 2 days**

### 5.1 Objective
Consolidate repetitive Tkinter dialog creation patterns into a `DialogFactory` class.

### 5.2 Target Functions and Line Ranges

#### Primary Targets:
1. **Surface selection dialogs** (lines ~3500-4100) - ~600 lines
2. **Parameter input dialogs** (scattered throughout)
3. **File selection and validation dialogs**

#### Repetitive Patterns:
- Tkinter window setup and configuration
- Widget creation and layout
- Event handling and validation
- Result collection and return

### 5.3 Implementation Plan

#### 5.3.1 DialogFactory Class Structure
```python
class DialogFactory:
    """Factory for creating common dialog patterns"""

    @staticmethod
    def create_selection_dialog(title, options, multi_select=False, **kwargs):
        """Generic selection dialog with common patterns"""
        root = tk.Tk()
        root.withdraw()

        dialog = tk.Toplevel()
        dialog.title(title)
        dialog.geometry(kwargs.get('geometry', '400x300'))
        dialog.grab_set()

        # Common frame setup
        main_frame = ttk.Frame(dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Instructions
        if 'instructions' in kwargs:
            ttk.Label(main_frame, text=kwargs['instructions'],
                     wraplength=380).pack(pady=(0, 10))

        # Selection widget
        if multi_select:
            selection_widget = DialogFactory._create_multi_selection(main_frame, options)
        else:
            selection_widget = DialogFactory._create_single_selection(main_frame, options)

        # Buttons
        result = {'selection': None, 'cancelled': False}
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=(10, 0))

        def on_ok():
            result['selection'] = DialogFactory._get_selection(selection_widget, multi_select)
            dialog.destroy()

        def on_cancel():
            result['cancelled'] = True
            dialog.destroy()

        ttk.Button(button_frame, text="OK", command=on_ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side=tk.LEFT, padx=5)

        # Center and show dialog
        DialogFactory._center_dialog(dialog)
        dialog.wait_window()
        root.destroy()

        return None if result['cancelled'] else result['selection']

    @staticmethod
    def create_surface_selection_dialog(las_files, **kwargs):
        """Specialized surface selection dialog for depth range selection"""
        # Extract surface data from LAS files
        all_surfaces = {}
        for las in las_files:
            well_name = las.well.WELL.value
            surfaces = DialogFactory._extract_surfaces_from_las(las)
            all_surfaces[well_name] = surfaces

        # Create dialog with well-specific surface selection
        root = tk.Tk()
        root.title("Select Depth Ranges for Analysis")
        root.geometry("800x600")

        # Create scrollable frame
        canvas = tk.Canvas(root)
        scrollbar = ttk.Scrollbar(root, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Well selection widgets
        selections = {}
        for well_name, surfaces in all_surfaces.items():
            well_frame = ttk.LabelFrame(scrollable_frame, text=f"Well: {well_name}", padding="10")
            well_frame.pack(fill=tk.X, padx=10, pady=5)

            selections[well_name] = DialogFactory._create_well_surface_selection(
                well_frame, well_name, surfaces
            )

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Result collection
        result = {'depth_ranges': None, 'cancelled': False}

        def on_submit():
            depth_ranges = {}
            for well_name, selection in selections.items():
                top_surface = selection['top_surface'].get()
                bottom_surface = selection['bottom_surface'].get()

                top_depth = DialogFactory._get_surface_depth(all_surfaces[well_name], top_surface)
                bottom_depth = DialogFactory._get_surface_depth(all_surfaces[well_name], bottom_surface)

                depth_ranges[well_name] = (top_depth, bottom_depth)

            result['depth_ranges'] = depth_ranges
            root.destroy()

        def on_cancel():
            result['cancelled'] = True
            root.destroy()

        # Buttons
        button_frame = ttk.Frame(root)
        button_frame.pack(pady=10)
        ttk.Button(button_frame, text="Submit", command=on_submit).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side=tk.LEFT, padx=5)

        root.mainloop()

        return None if result['cancelled'] else result['depth_ranges']
```

### 5.4 Implementation Steps

1. **Analyze dialog patterns** (Day 1)
2. **Create DialogFactory class** (Day 1)
3. **Consolidate selection dialogs** (Day 1-2)
4. **Update dialog function calls** (Day 2)
5. **Testing and UI validation** (Day 2)

---

## Phase 6: Main Workflow Optimization
**Priority: LOW | Estimated Reduction: 200-300 lines | Timeline: 1-2 days**

### 6.1 Objective
Simplify the main execution function and create a step-by-step workflow orchestrator.

### 6.2 Target Functions and Line Ranges

#### Primary Targets:
1. **`run_eei_analysis`** (lines ~4600-4857) - ~257 lines
2. **Main execution logic** (lines ~4860-4887) - ~27 lines

### 6.3 Implementation Plan

#### 6.3.1 EEIAnalysisWorkflow Class Structure
```python
class EEIAnalysisWorkflow:
    """Main workflow orchestrator"""

    def __init__(self):
        self.las_files = None
        self.analysis_config = None
        self.results = None
        self.logger = logging.getLogger(__name__)

    def run(self):
        """Main workflow execution with step-by-step approach"""
        workflow_steps = [
            ('Loading Files', self._load_files),
            ('Validating Logs', self._validate_logs),
            ('Calculating Derived Logs', self._calculate_derived_logs),
            ('Getting User Inputs', self._get_user_inputs),
            ('Performing Analysis', self._perform_analysis),
            ('Generating Plots', self._generate_plots),
            ('Showing Results', self._show_results)
        ]

        for step_name, step_func in workflow_steps:
            self.logger.info(f"Starting step: {step_name}")

            try:
                if not step_func():
                    self.logger.warning(f"Step failed or cancelled: {step_name}")
                    return False
                self.logger.info(f"Completed step: {step_name}")
            except Exception as e:
                self.logger.error(f"Error in step {step_name}: {str(e)}")
                return False

        return True

    def _load_files(self):
        """File loading step"""
        self.las_files = load_multiple_las_files()
        return len(self.las_files) > 0

    def _validate_logs(self):
        """Log validation step"""
        validation_results = validate_essential_logs(self.las_files, LOG_KEYWORDS)
        validation_summary = generate_validation_report(validation_results)

        if validation_summary['invalid_files'] > 0:
            return self._ask_continue_with_missing_logs(validation_summary)
        return True

    def _calculate_derived_logs(self):
        """Optional derived log calculation step"""
        if messagebox.askyesno("Custom Calculations",
                              "Do you want to add custom log calculations?"):
            return get_calculations_for_eei(self.las_files)
        return True

    def _get_user_inputs(self):
        """Consolidate all user input gathering"""
        # Get analysis method
        self.analysis_config = self._get_analysis_method()
        if not self.analysis_config:
            return False

        # Get target log
        self.analysis_config['target_log'] = self._get_target_log_selection()
        if not self.analysis_config['target_log']:
            return False

        # Get depth ranges
        self.analysis_config['depth_ranges'] = DialogFactory.create_surface_selection_dialog(self.las_files)
        if not self.analysis_config['depth_ranges']:
            return False

        # Get analysis type (individual vs merged)
        self.analysis_config['analysis_type'] = self._get_analysis_type()
        if not self.analysis_config['analysis_type']:
            return False

        return True

    def _perform_analysis(self):
        """Perform the main analysis"""
        analyzer = WellAnalyzer(self.las_files, LOG_KEYWORDS, self.analysis_config)

        if self.analysis_config['analysis_type'] == 'individual':
            self.results = analyzer.analyze(
                self.analysis_config['target_log'],
                self.analysis_config['depth_ranges'],
                'individual'
            )
        else:
            self.results = analyzer.analyze(
                self.analysis_config['target_log'],
                self.analysis_config['depth_ranges'],
                'merged'
            )

        return self.results is not None

    def _generate_plots(self):
        """Generate all plots"""
        if not self.results:
            return False

        # Filter plottable data
        plottable_data = [
            data for data in self.results[1]  # results[1] contains well data
            if data.get('depth') is not None and
               data.get('target') is not None and
               data.get('normalized_eei') is not None
        ]

        if not plottable_data:
            self.logger.warning("No plottable data available after analysis")
            return True  # Not a failure, just no plots to show

        # Generate plots using PlotGenerator
        PlotGenerator.plot_well_comparison(
            plottable_data,
            self.analysis_config['target_log'],
            self.analysis_config['depth_ranges'],
            self.analysis_config['analysis_method']
        )

        return True

    def _show_results(self):
        """Show final results and next action dialog"""
        print("Analysis complete.")

        # Show next action dialog
        user_choice = show_next_action_dialog()

        if user_choice == 'restart':
            print("\n" + "="*80)
            print("🔄 STARTING NEW ANALYSIS")
            print("="*80)
            return True  # Signal to restart
        else:
            print("\n" + "="*80)
            print("🚪 EXITING PROGRAM")
            print("="*80)
            return False  # Signal to exit
```

### 6.4 Implementation Steps

1. **Create workflow orchestrator** (Day 1)
2. **Break down main function into steps** (Day 1)
3. **Update main execution** (Day 1-2)
4. **Testing and integration** (Day 2)

---

## Overall Implementation Strategy

### Timeline Summary
- **Phase 3**: 3-4 days (600-800 line reduction)
- **Phase 4**: 2-3 days (300-400 line reduction)
- **Phase 5**: 2 days (200-300 line reduction)
- **Phase 6**: 1-2 days (200-300 line reduction)
- **Total**: 8-11 days (1,300-1,800 line reduction)

### Final Expected Results
- **Current**: 4,887 lines
- **After Phases 3-6**: 2,587-3,087 lines
- **Total Reduction**: 1,855-2,355 lines (38-48% reduction)
- **Target Achievement**: ✅ Meets 40-50% reduction goal

### Risk Mitigation
1. **Incremental Implementation**: Complete one phase before starting the next
2. **Comprehensive Testing**: Test each phase thoroughly before proceeding
3. **Legacy Compatibility**: Maintain wrapper functions during transition
4. **Version Control**: Create feature branches for each phase
5. **Rollback Plan**: Keep original functions commented until validation complete

### Testing Strategy

#### Phase-by-Phase Testing Approach

**Phase 3 Testing (WellAnalyzer):**
```python
def test_well_analyzer():
    """Test WellAnalyzer functionality"""
    # Test individual well analysis
    analyzer = WellAnalyzer(test_las_files, LOG_KEYWORDS, test_config)
    results = analyzer.analyze('PHIT', test_depth_ranges, 'individual')

    # Verify results structure
    assert len(results) == 2  # results and data
    assert len(results[0]) == len(test_las_files)  # One result per well

    # Test merged well analysis
    merged_results = analyzer.analyze('PHIT', test_depth_ranges, 'merged')
    assert merged_results is not None

    # Compare with legacy functions
    legacy_results = individual_well_analysis(test_las_files, 'PHIT', test_depth_ranges)
    assert_results_equivalent(results, legacy_results)
```

**Phase 4 Testing (PlotGenerator):**
```python
def test_plot_generator():
    """Test PlotGenerator functionality"""
    # Test heatmap generation
    PlotGenerator.plot_correlation_heatmap(
        test_correlation_matrix, test_n_values, test_phi_values,
        optimal_n=1.5, optimal_phi=45, analysis_type='CPEI',
        target_log='PHIT', well_name='TEST_WELL'
    )

    # Verify plot was created (check matplotlib state)
    assert plt.get_fignums()  # At least one figure exists
    plt.close('all')  # Clean up
```

**Phase 5 Testing (DialogFactory):**
```python
def test_dialog_factory():
    """Test DialogFactory functionality"""
    # Mock user interactions for automated testing
    with patch('tkinter.messagebox.askyesno', return_value=True):
        result = DialogFactory.create_selection_dialog(
            "Test Dialog", ["Option 1", "Option 2", "Option 3"]
        )
        assert result in ["Option 1", "Option 2", "Option 3"]
```

**Phase 6 Testing (EEIAnalysisWorkflow):**
```python
def test_workflow():
    """Test complete workflow"""
    workflow = EEIAnalysisWorkflow()

    # Mock all user interactions
    with patch.multiple(
        workflow,
        _load_files=Mock(return_value=True),
        _validate_logs=Mock(return_value=True),
        _get_user_inputs=Mock(return_value=True),
        _perform_analysis=Mock(return_value=True),
        _generate_plots=Mock(return_value=True),
        _show_results=Mock(return_value=False)
    ):
        result = workflow.run()
        assert result is False  # Workflow completed and chose to exit
```

#### Integration Testing
```python
def test_end_to_end_integration():
    """Test complete refactored system against original"""
    # Load same test data
    las_files = load_test_las_files()

    # Run original system
    original_results = run_original_analysis(las_files, test_config)

    # Run refactored system
    workflow = EEIAnalysisWorkflow()
    refactored_results = workflow.run_with_config(test_config)

    # Compare results (allowing for minor numerical differences)
    assert_results_equivalent(original_results, refactored_results, tolerance=1e-6)
```

### Performance Benchmarking

#### Memory Usage Comparison
```python
def benchmark_memory_usage():
    """Compare memory usage before and after refactoring"""
    import tracemalloc

    # Test original functions
    tracemalloc.start()
    original_results = run_original_cpei_analysis()
    original_memory = tracemalloc.get_traced_memory()[1]
    tracemalloc.stop()

    # Test refactored classes
    tracemalloc.start()
    calculator = ImpedanceCalculator('CPEI')
    refactored_results = calculator.optimize_parameters(test_data)
    refactored_memory = tracemalloc.get_traced_memory()[1]
    tracemalloc.stop()

    # Memory usage should be similar or better
    assert refactored_memory <= original_memory * 1.1  # Allow 10% increase
```

#### Execution Time Comparison
```python
def benchmark_execution_time():
    """Compare execution time before and after refactoring"""
    import time

    # Time original functions
    start_time = time.time()
    original_results = run_original_analysis()
    original_time = time.time() - start_time

    # Time refactored classes
    start_time = time.time()
    refactored_results = run_refactored_analysis()
    refactored_time = time.time() - start_time

    # Performance should be similar or better
    assert refactored_time <= original_time * 1.2  # Allow 20% increase
```

### Risk Mitigation Strategies

#### 1. **Incremental Rollout**
- Implement one phase at a time
- Keep original functions as fallbacks during transition
- Use feature flags to switch between old and new implementations

#### 2. **Comprehensive Testing**
- Unit tests for each new class
- Integration tests for class interactions
- End-to-end tests comparing original vs refactored results
- Performance regression tests

#### 3. **Version Control Strategy**
```bash
# Create feature branches for each phase
git checkout -b feature/phase-3-well-analyzer
git checkout -b feature/phase-4-plot-generator
git checkout -b feature/phase-5-dialog-factory
git checkout -b feature/phase-6-workflow-orchestrator

# Merge phases incrementally after testing
git checkout main
git merge feature/phase-3-well-analyzer
# Test thoroughly before proceeding
git merge feature/phase-4-plot-generator
```

#### 4. **Rollback Plan**
- Maintain commented original functions until validation complete
- Create backup branches before each phase
- Document all changes for easy reversal

### Success Metrics

#### Quantitative Metrics
- [ ] **Code Reduction**: 40-50% reduction achieved (from 4,942 to ~2,500-3,000 lines)
- [ ] **Duplication Elimination**: >90% of identified duplicate code removed
- [ ] **Performance**: No more than 20% performance degradation
- [ ] **Memory Usage**: No more than 10% memory increase
- [ ] **Test Coverage**: >95% test coverage for new classes

#### Qualitative Metrics
- [ ] **Maintainability**: Easier to add new impedance types
- [ ] **Readability**: Code is more self-documenting
- [ ] **Modularity**: Clear separation of concerns
- [ ] **Extensibility**: Easy to add new analysis methods
- [ ] **Error Handling**: Improved error reporting and logging

#### Functional Metrics
- [ ] **All existing functionality preserved**
- [ ] **No breaking changes to user interface**
- [ ] **All test cases pass**
- [ ] **No regression in analysis accuracy**
- [ ] **Successful validation with real-world data**

### Final Implementation Timeline

| Phase | Duration | Key Deliverables | Dependencies |
|-------|----------|------------------|--------------|
| **Phase 3** | 3-4 days | WellAnalyzer class, consolidated analysis workflows | Phases 1-2 complete |
| **Phase 4** | 2-3 days | PlotGenerator class, unified plotting functions | Phase 3 complete |
| **Phase 5** | 2 days | DialogFactory class, simplified dialog creation | Independent |
| **Phase 6** | 1-2 days | EEIAnalysisWorkflow class, main function simplification | Phases 3-5 complete |
| **Testing** | 2-3 days | Comprehensive test suite, performance validation | All phases complete |
| **Documentation** | 1 day | Updated documentation, migration guide | All phases complete |

**Total Estimated Duration: 11-15 days**

### Expected Final Results

- **Original File Size**: 4,942 lines
- **Current File Size**: 4,887 lines (after Phases 1-2)
- **Final Expected Size**: 2,587-3,087 lines
- **Total Reduction**: 1,855-2,355 lines (38-48% reduction)
- **Target Achievement**: ✅ **Meets 40-50% reduction goal**

The refactoring project will successfully achieve the target code reduction while improving maintainability, readability, and extensibility of the EEI Analysis Tool.
