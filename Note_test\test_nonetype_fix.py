#!/usr/bin/env python3
"""
Test script to verify NoneType format string error fixes in CPEI analysis.

This script tests the safe formatting functions and error handling
to ensure no NoneType format string errors occur during CPEI/PEIL analysis.
"""

import numpy as np
import sys
import os

# Add the current directory to the path to import the main script functions
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the safe formatting functions from the main script
try:
    from a6_load_multilas_EEI_XCOR_PLOT_Final import (
        safe_format_float,
        safe_format_parameter_string,
        validate_cpei_peil_inputs
    )
    print("✅ Successfully imported safe formatting functions")
except ImportError as e:
    print(f"❌ Failed to import functions: {e}")
    sys.exit(1)

def test_safe_format_float():
    """Test the safe_format_float function with various inputs."""
    print("\n🧪 Testing safe_format_float function...")
    
    test_cases = [
        (None, "N/A"),
        (1.23456, "1.2346"),
        (0.0, "0.0000"),
        (-1.5, "-1.5000"),
        (np.nan, "N/A"),
        (np.inf, "N/A"),
        (-np.inf, "N/A"),
        ("invalid", "N/A"),
        ([], "N/A"),
        ({}, "N/A")
    ]
    
    for i, (input_val, expected_pattern) in enumerate(test_cases):
        try:
            result = safe_format_float(input_val)
            print(f"  Test {i+1}: Input={input_val} -> Output='{result}' ✅")
        except Exception as e:
            print(f"  Test {i+1}: Input={input_val} -> ERROR: {e} ❌")
            return False
    
    return True

def test_safe_format_parameter_string():
    """Test the safe_format_parameter_string function with various inputs."""
    print("\n🧪 Testing safe_format_parameter_string function...")
    
    test_cases = [
        (None, None, "n=None, phi=None"),
        (1.5, 45, "n=1.5, phi=45°"),
        (None, 45, "n=None, phi=45°"),
        (1.5, None, "n=1.5, phi=None"),
        (np.nan, 45, "n=N/A, phi=45°"),
        (1.5, np.nan, "n=1.5, phi=nan°"),
        ("invalid", "invalid", "n=invalid, phi=invalid"),
    ]
    
    for i, (n_val, phi_val, expected_pattern) in enumerate(test_cases):
        try:
            result = safe_format_parameter_string(n_val, phi_val)
            print(f"  Test {i+1}: n={n_val}, phi={phi_val} -> '{result}' ✅")
        except Exception as e:
            print(f"  Test {i+1}: n={n_val}, phi={phi_val} -> ERROR: {e} ❌")
            return False
    
    return True

def test_validate_cpei_peil_inputs():
    """Test the validate_cpei_peil_inputs function with various inputs."""
    print("\n🧪 Testing validate_cpei_peil_inputs function...")
    
    # Valid inputs
    valid_pvel = np.array([3000, 3100, 3200])
    valid_svel = np.array([1500, 1550, 1600])
    valid_rhob = np.array([2.3, 2.4, 2.5])
    
    test_cases = [
        # Valid case
        (valid_pvel, valid_svel, valid_rhob, 1.5, 45, True),
        # None inputs
        (None, valid_svel, valid_rhob, 1.5, 45, False),
        (valid_pvel, None, valid_rhob, 1.5, 45, False),
        (valid_pvel, valid_svel, None, 1.5, 45, False),
        (valid_pvel, valid_svel, valid_rhob, None, 45, False),
        (valid_pvel, valid_svel, valid_rhob, 1.5, None, False),
        # Empty arrays
        (np.array([]), valid_svel, valid_rhob, 1.5, 45, False),
        # NaN arrays
        (np.array([np.nan, np.nan]), valid_svel, valid_rhob, 1.5, 45, False),
        # Invalid parameter ranges
        (valid_pvel, valid_svel, valid_rhob, -1.0, 45, False),
        (valid_pvel, valid_svel, valid_rhob, 1.5, 100, False),
    ]
    
    for i, (pvel, svel, rhob, n, phi, expected_valid) in enumerate(test_cases):
        try:
            result = validate_cpei_peil_inputs(pvel, svel, rhob, n, phi, "CPEI")
            is_valid = result['valid']
            if is_valid == expected_valid:
                print(f"  Test {i+1}: Expected valid={expected_valid}, got valid={is_valid} ✅")
            else:
                print(f"  Test {i+1}: Expected valid={expected_valid}, got valid={is_valid} ❌")
                print(f"    Errors: {result['errors']}")
                return False
        except Exception as e:
            print(f"  Test {i+1}: ERROR: {e} ❌")
            return False
    
    return True

def test_format_string_scenarios():
    """Test specific format string scenarios that could cause NoneType errors."""
    print("\n🧪 Testing format string scenarios...")
    
    # Test scenarios that previously caused NoneType errors
    test_scenarios = [
        # Scenario 1: None values in f-string formatting
        (None, None),
        (np.nan, np.nan),
        (1.5, None),
        (None, 45),
        # Scenario 2: Invalid numeric values
        ("invalid", "invalid"),
        ([], {}),
    ]
    
    for i, (n_val, phi_val) in enumerate(test_scenarios):
        try:
            # Test the safe parameter string formatting
            param_str = safe_format_parameter_string(n_val, phi_val)
            
            # Test individual float formatting
            n_str = safe_format_float(n_val, precision=1)
            phi_str = f"{phi_val}°" if phi_val is not None else "N/A"
            
            print(f"  Scenario {i+1}: n={n_val}, phi={phi_val}")
            print(f"    Parameter string: '{param_str}'")
            print(f"    Individual formatting: n='{n_str}', phi='{phi_str}' ✅")
            
        except Exception as e:
            print(f"  Scenario {i+1}: ERROR: {e} ❌")
            return False
    
    return True

def main():
    """Run all tests."""
    print("🚀 Starting NoneType format string error fix tests...")
    print("="*60)
    
    tests = [
        test_safe_format_float,
        test_safe_format_parameter_string,
        test_validate_cpei_peil_inputs,
        test_format_string_scenarios
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_func.__name__} PASSED")
            else:
                print(f"❌ {test_func.__name__} FAILED")
        except Exception as e:
            print(f"❌ {test_func.__name__} FAILED with exception: {e}")
    
    print("\n" + "="*60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! NoneType format string errors should be fixed.")
        return True
    else:
        print("⚠️ Some tests failed. Please review the error handling.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)