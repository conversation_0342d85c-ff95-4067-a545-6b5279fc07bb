#!/usr/bin/env python3
"""
Test script to verify the dialog hanging fix in b4_Xplot_HIST_KDE_FUNCT_Custom.py
"""

import sys
import os

# Add the current directory to Python path so we can import the module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the necessary functions from the main module
try:
    from b4_Xplot_HIST_KDE_FUNCT_Custom import (
        handle_calculation_error,
        console_fallback_dialog,
        WindowManager
    )
    print("✅ Successfully imported functions from b4_Xplot_HIST_KDE_FUNCT_Custom.py")
except ImportError as e:
    print(f"❌ Failed to import functions: {e}")
    sys.exit(1)

def test_console_fallback():
    """Test the console fallback dialog."""
    print("\n" + "="*50)
    print("TESTING CONSOLE FALLBACK DIALOG")
    print("="*50)
    
    # Test with dictionary format
    error_details = {
        'valid': False,
        'error_details': 'Test error message for console fallback.\nThis is a multi-line error message.'
    }
    
    print("Testing console fallback with dictionary format...")
    # Note: This will actually prompt for user input
    # result = console_fallback_dialog(error_details)
    # print(f"Console fallback returned: {result}")
    print("Console fallback test skipped (would require user input)")

def test_window_manager():
    """Test the WindowManager singleton."""
    print("\n" + "="*50)
    print("TESTING WINDOW MANAGER")
    print("="*50)
    
    try:
        # Test singleton behavior
        wm1 = WindowManager()
        wm2 = WindowManager()
        
        if wm1 is wm2:
            print("✅ WindowManager singleton working correctly")
        else:
            print("❌ WindowManager singleton not working")
            
        # Test root window creation
        root = wm1.get_root()
        print(f"✅ Root window created: {root}")
        
        # Test dialog creation
        dialog = wm1.create_dialog("Test Dialog", "300x200")
        print(f"✅ Dialog created: {dialog}")
        
        # Clean up
        dialog.destroy()
        print("✅ Dialog destroyed successfully")
        
    except Exception as e:
        print(f"❌ WindowManager test failed: {e}")
        import traceback
        traceback.print_exc()

def test_error_dialog():
    """Test the error dialog with timeout."""
    print("\n" + "="*50)
    print("TESTING ERROR DIALOG")
    print("="*50)
    
    # Create mock error details
    error_details = {
        'valid': False,
        'error_details': '''Calculator Validation Error:

The following issues were found:
1. Missing required log curves
2. Invalid calculation syntax
3. Data range conflicts

Please review your calculations and try again.'''
    }
    
    # Create mock LAS files (empty list for testing)
    las_files = []
    
    print("Testing error dialog (will auto-close after 10 seconds)...")
    print("You can click any button to test the dialog interaction.")
    
    try:
        result = handle_calculation_error(error_details, las_files)
        print(f"✅ Error dialog returned: {result}")
    except Exception as e:
        print(f"❌ Error dialog test failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run all tests."""
    print("Dialog Fix Test Suite")
    print("=" * 50)
    
    # Test 1: Window Manager
    test_window_manager()
    
    # Test 2: Console Fallback (non-interactive)
    test_console_fallback()
    
    # Test 3: Error Dialog (interactive)
    print("\nWould you like to test the error dialog? (y/n): ", end="")
    try:
        response = input().lower().strip()
        if response in ['y', 'yes']:
            test_error_dialog()
        else:
            print("Skipping interactive error dialog test")
    except (EOFError, KeyboardInterrupt):
        print("\nSkipping interactive test")
    
    print("\n" + "="*50)
    print("TEST SUITE COMPLETED")
    print("="*50)
    
    # Clean up
    try:
        wm = WindowManager()
        wm.cleanup()
        print("✅ WindowManager cleanup completed")
    except Exception as e:
        print(f"⚠️ Cleanup warning: {e}")

if __name__ == "__main__":
    main()