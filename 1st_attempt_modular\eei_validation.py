# -*- coding: utf-8 -*-
"""
EEI Analysis Validation Module

This module provides unified validation for all EEI analysis types with proper input/output variable distinction.
Created as part of the systematic refactoring of a7_load_multilas_EEI_XCOR_PLOT_Final.py

@author: devri.agustianto
"""

import numpy as np
import re
import logging
from eei_config import VALIDATION_RANGES, ERROR_MESSAGES, USER_GUIDANCE, ANALYSIS_TYPES

# Configure logging
logger = logging.getLogger(__name__)

class EEIValidator:
    """Centralized validation for all EEI analysis types with enhanced input/output variable handling"""
    
    @staticmethod
    def validate_analysis_inputs(analysis_type, pvel, svel, rhob, params, target=None):
        """
        Unified validation for EEI/CPEI/PEIL inputs with proper input/output distinction.
        
        Args:
            analysis_type: 'EEI', 'CPEI', or 'PEIL'
            pvel: P-wave velocity array (INPUT - must exist)
            svel: S-wave velocity array (INPUT - must exist)
            rhob: Density array (INPUT - must exist)
            params: Parameters dict for the analysis type
            target: Target array (OUTPUT - optional for correlation)
            
        Returns:
            dict: {
                'valid': bool,
                'errors': list,
                'input_arrays_valid': bool,
                'output_target_valid': bool
            }
        """
        errors = []
        
        try:
            # Validate INPUT arrays (these must exist and be valid)
            input_arrays = {'pvel': pvel, 'svel': svel, 'rhob': rhob}
            for name, array in input_arrays.items():
                if array is None:
                    errors.append(f"Input array '{name}' is None")
                elif not hasattr(array, 'size') or array.size == 0:
                    errors.append(f"Input array '{name}' is empty")
                elif not np.isfinite(array).any():
                    errors.append(f"Input array '{name}' contains no finite values")
            
            # Validate OUTPUT target (if provided for correlation analysis)
            target_valid = True
            if target is not None:
                if not hasattr(target, 'size') or target.size == 0:
                    errors.append("Output target array is empty")
                    target_valid = False
                elif not np.isfinite(target).any():
                    errors.append("Output target array contains no finite values")
                    target_valid = False
            
            # Validate parameters based on analysis type
            if analysis_type == 'EEI':
                errors.extend(EEIValidator._validate_eei_params(params))
            elif analysis_type in ['CPEI', 'PEIL']:
                errors.extend(EEIValidator._validate_cpei_peil_params(params, analysis_type))
            else:
                errors.append(f"Unknown analysis type: {analysis_type}")
            
            if errors:
                logger.warning(f"{analysis_type} input validation failed: {'; '.join(errors)}")
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'input_arrays_valid': len([e for e in errors if 'Input array' in e]) == 0,
                'output_target_valid': target_valid
            }
            
        except Exception as e:
            error_msg = f"Unexpected error during {analysis_type} input validation: {str(e)}"
            logger.error(error_msg)
            return {
                'valid': False,
                'errors': [error_msg],
                'input_arrays_valid': False,
                'output_target_valid': False
            }
    
    @staticmethod
    def _validate_eei_params(params):
        """Validate EEI-specific parameters"""
        errors = []
        
        # Validate angle parameter
        angle = params.get('angle')
        if angle is None:
            errors.append("EEI angle parameter is None")
        else:
            try:
                angle_float = float(angle)
                angle_range = VALIDATION_RANGES['angle_parameter']
                if angle_float < angle_range['min'] or angle_float > angle_range['max']:
                    errors.append(f"EEI angle ({angle_float}) is outside range [{angle_range['min']}, {angle_range['max']}]")
            except (ValueError, TypeError):
                errors.append(f"EEI angle ({angle}) is not a valid number")
        
        # Validate k parameter
        k = params.get('k')
        if k is not None:  # k can be None for calculated method
            try:
                k_float = float(k)
                k_range = VALIDATION_RANGES['k_parameter']
                if k_float <= k_range['min'] or k_float > k_range['max']:
                    errors.append(f"EEI k parameter ({k_float}) is outside range ({k_range['min']}, {k_range['max']}]")
            except (ValueError, TypeError):
                errors.append(f"EEI k parameter ({k}) is not a valid number")
        
        return errors
    
    @staticmethod
    def _validate_cpei_peil_params(params, analysis_type):
        """Validate CPEI/PEIL-specific parameters"""
        errors = []
        
        # Validate n parameter
        n = params.get('n')
        if n is None:
            errors.append(f"{analysis_type} parameter n is None")
        else:
            try:
                n_float = float(n)
                n_range = VALIDATION_RANGES['n_parameter']
                if n_float <= n_range['min'] or n_float > n_range['max']:
                    errors.append(f"{analysis_type} parameter n ({n_float}) is outside reasonable range ({n_range['min']}, {n_range['max']}]")
            except (ValueError, TypeError):
                errors.append(f"{analysis_type} parameter n ({n}) is not a valid number")
        
        # Validate phi parameter
        phi = params.get('phi')
        if phi is None:
            errors.append(f"{analysis_type} parameter phi is None")
        else:
            try:
                phi_float = float(phi)
                phi_range = VALIDATION_RANGES['phi_parameter']
                if phi_float < phi_range['min'] or phi_float > phi_range['max']:
                    errors.append(f"{analysis_type} parameter phi ({phi_float}) is outside range [{phi_range['min']}, {phi_range['max']}]")
            except (ValueError, TypeError):
                errors.append(f"{analysis_type} parameter phi ({phi}) is not a valid number")
        
        return errors
    
    @staticmethod
    def validate_calculation_inputs_enhanced(las_files, calculation_text):
        """
        Enhanced validation distinguishing input vs output variables.
        
        This addresses the input/output variable distinction issue mentioned in memories.
        Input variables must exist in all wells, output variables are being created.
        
        Args:
            las_files: List of lasio.LASFile objects
            calculation_text: String containing the calculation code
            
        Returns:
            dict: {
                'valid': bool,
                'missing_input_logs': dict,  # {well_name: [missing_log_names]}
                'output_variables': list,
                'input_variables': list,
                'error_details': str
            }
        """
        print("🔄 Enhanced Validation: Starting input/output variable analysis...")
        
        # Parse calculation text to separate output variables from input variables
        lines = [line.strip() for line in calculation_text.split('\n') if line.strip()]
        output_variables = set()
        input_variables = set()
        
        for line in lines:
            if line.startswith('#'):
                continue
                
            # Look for assignment operations
            if '=' in line and not any(op in line for op in ['==', '!=', '<=', '>=']):
                if any(compound in line for compound in ['+=', '-=', '*=', '/=']):
                    # Compound assignment - left side is both input and output
                    left_side = line.split('=')[0].strip()
                    right_side = '='.join(line.split('=')[1:]).strip()
                else:
                    # Regular assignment
                    parts = line.split('=', 1)
                    if len(parts) == 2:
                        left_side = parts[0].strip()
                        right_side = parts[1].strip()
                    else:
                        continue
                
                # Extract OUTPUT variable name (left side of assignment)
                output_var = re.match(r'^([A-Z_][A-Z0-9_]*)', left_side.upper())
                if output_var:
                    output_variables.add(output_var.group(1))
                
                # Extract INPUT variables from right side
                right_vars = set(re.findall(r'\b([A-Z_][A-Z0-9_]*)\b', right_side.upper()))
                input_variables.update(right_vars)
            else:
                # No assignment, treat all variables as input
                line_vars = set(re.findall(r'\b([A-Z_][A-Z0-9_]*)\b', line.upper()))
                input_variables.update(line_vars)
        
        # Remove numpy functions, Python keywords, and output variables from input variables
        excluded_terms = {'NP', 'NUMPY', 'LOG', 'SQRT', 'EXP', 'SIN', 'COS', 'TAN', 'NANMIN', 'NANMAX', 
                         'NANMEAN', 'NANSTD', 'WHERE', 'ISNAN', 'ISFINITE', 'MEAN', 'STD', 'MIN', 'MAX', 'ABS',
                         'IF', 'ELSE', 'FOR', 'WHILE', 'DEF', 'CLASS', 'IMPORT', 'FROM', 'AS', 'RETURN', 
                         'TRUE', 'FALSE', 'NONE'}
        
        input_variables = input_variables - output_variables - excluded_terms
        
        print(f"🔄 Enhanced Validation: Output variables (being created): {sorted(output_variables)}")
        print(f"🔄 Enhanced Validation: Input variables (must exist): {sorted(input_variables)}")
        
        # Check availability of INPUT variables only across all wells
        missing_input_logs = {}
        for las in las_files:
            well_name = las.well.WELL.value
            well_logs = set(las.curves.keys())
            missing_in_well = input_variables - well_logs
            
            if missing_in_well:
                missing_input_logs[well_name] = list(missing_in_well)
        
        # Generate enhanced error details
        is_valid = len(missing_input_logs) == 0
        error_details = EEIValidator._generate_enhanced_error_details(
            is_valid, missing_input_logs, output_variables, input_variables, las_files
        )
        
        return {
            'valid': is_valid,
            'missing_input_logs': missing_input_logs,
            'output_variables': list(output_variables),
            'input_variables': list(input_variables),
            'error_details': error_details
        }
    
    @staticmethod
    def _generate_enhanced_error_details(is_valid, missing_input_logs, output_variables, input_variables, las_files):
        """Generate detailed error report with input/output variable distinction"""
        if is_valid:
            return ""
        
        guidance = USER_GUIDANCE['input_vs_output']
        error_details = f"{ERROR_MESSAGES['missing_input_logs']}\n\n"
        error_details += "⚠️ WARNING: Your calculations reference INPUT logs that are NOT available in all wells.\n"
        error_details += "This will cause calculation failures in wells where these logs are missing.\n\n"
        
        if output_variables:
            error_details += f"{guidance['output_marker']} OUTPUT variables being created: {', '.join(sorted(output_variables))}\n"
            error_details += f"{guidance['input_marker']} These are fine - they will be created by your calculations.\n\n"
        
        error_details += f"{guidance['missing_marker']} MISSING INPUT LOGS:\n\n"
        
        for well_name, missing in missing_input_logs.items():
            error_details += f"Well: {well_name}\n"
            if missing:
                error_details += f"  {guidance['missing_marker']} Missing INPUT logs: {', '.join(missing)}\n"
            error_details += "\n"
        
        # Get common logs available in all wells
        common_logs = set(las_files[0].curves.keys())
        for las in las_files[1:]:
            common_logs.intersection_update(las.curves.keys())
        
        safe_input_logs = input_variables.intersection(common_logs)
        error_details += f"{guidance['input_marker']} SAFE INPUT LOGS (Available in ALL {len(las_files)} wells):\n"
        if safe_input_logs:
            safe_logs_list = sorted(safe_input_logs)
            for i in range(0, len(safe_logs_list), 5):
                error_details += f"   {', '.join(safe_logs_list[i:i+5])}\n"
        else:
            error_details += "   None of your referenced INPUT logs are available in all wells!\n"
        
        error_details += f"\n📋 ALL AVAILABLE LOGS (in all wells): {', '.join(sorted(common_logs))}\n"
        
        recovery = USER_GUIDANCE['recovery_options']
        error_details += "\n🔧 RECOVERY OPTIONS:\n"
        error_details += f"[Retry] - {recovery['retry']}\n"
        error_details += f"[Skip] - {recovery['skip']}\n"
        error_details += f"[Cancel] - {recovery['cancel']}"
        
        return error_details
    
    @staticmethod
    def analyze_log_availability(las_files):
        """
        Analyze which logs are available across all wells.
        
        Args:
            las_files: List of lasio.LASFile objects
            
        Returns:
            dict: {
                'common_logs': list,      # Available in all wells
                'partial_logs': dict,     # Available in some wells
                'total_wells': int        # Total number of wells
            }
        """
        all_logs = {}
        well_count = len(las_files)
        
        # Count log occurrences across wells
        for las in las_files:
            well_name = las.well.WELL.value
            for curve_name in las.curves.keys():
                if curve_name not in all_logs:
                    all_logs[curve_name] = {'wells': [], 'count': 0}
                all_logs[curve_name]['wells'].append(well_name)
                all_logs[curve_name]['count'] += 1
        
        # Categorize logs
        common_logs = [log for log, info in all_logs.items() if info['count'] == well_count]
        partial_logs = {log: info for log, info in all_logs.items()
                       if 0 < info['count'] < well_count}
        
        return {
            'common_logs': sorted(common_logs),
            'partial_logs': partial_logs,
            'total_wells': well_count
        }
