#!/usr/bin/env python3
"""
Test script to verify the CPEI formatting fixes.
This script tests the specific formatting issues that were causing the error.
"""

import numpy as np

def test_formatting_fixes():
    """Test the formatting fixes for CPEI analysis."""
    
    print("Testing CPEI formatting fixes...")
    
    # Test case 1: Normal numpy float64 value
    max_correlation_numpy = np.float64(0.3303)
    print(f"Test 1 - numpy.float64: {max_correlation_numpy}")
    
    try:
        correlation_value = float(max_correlation_numpy) if max_correlation_numpy is not None else 0.0
        formatted_result = f"Maximum correlation: {correlation_value:.4f}"
        print(f"✅ Success: {formatted_result}")
    except (ValueError, TypeError) as e:
        print(f"❌ Error: {e}")
        print(f"Fallback: Maximum correlation: {max_correlation_numpy}")
    
    # Test case 2: String value (should trigger fallback)
    max_correlation_string = "n=1.1, phi=-24°"
    print(f"\nTest 2 - string value: {max_correlation_string}")
    
    try:
        correlation_value = float(max_correlation_string) if max_correlation_string is not None else 0.0
        formatted_result = f"Maximum correlation: {correlation_value:.4f}"
        print(f"✅ Success: {formatted_result}")
    except (ValueError, TypeError) as e:
        print(f"Expected error caught: {e}")
        print(f"✅ Fallback works: Maximum correlation: {max_correlation_string}")
    
    # Test case 3: None value
    max_correlation_none = None
    print(f"\nTest 3 - None value: {max_correlation_none}")
    
    try:
        correlation_value = float(max_correlation_none) if max_correlation_none is not None else 0.0
        formatted_result = f"Maximum correlation: {correlation_value:.4f}"
        print(f"✅ Success: {formatted_result}")
    except (ValueError, TypeError) as e:
        print(f"❌ Error: {e}")
        print(f"Fallback: Maximum correlation: {max_correlation_none}")
    
    # Test case 4: Summary plotting logic
    print(f"\nTest 4 - Summary plotting logic:")
    
    # Simulate CPEI results
    cpei_results = [
        {'well_name': 'Well-1', 'optimum_angle': 'n=1.1, phi=-24°', 'max_correlation': 0.3303},
        {'well_name': 'Well-2', 'optimum_angle': 'n=1.5, phi=15°', 'max_correlation': 0.4567}
    ]
    
    # Test EEI analysis type (analysis_method = 1)
    analysis_method = 1
    print(f"EEI analysis (analysis_method={analysis_method}):")
    for i, result in enumerate(cpei_results):
        if analysis_method == 1:  # EEI - optimum_angle should be numeric
            # This would fail for CPEI data, but let's test the logic
            try:
                angle_text = f"{result['optimum_angle']:.1f}°"
                y_position = result['optimum_angle']
                print(f"  Well {i}: angle_text='{angle_text}', y_position={y_position}")
            except (ValueError, TypeError) as e:
                print(f"  ❌ Expected error for EEI with CPEI data: {e}")
    
    # Test CPEI analysis type (analysis_method = 2)
    analysis_method = 2
    print(f"\nCPEI analysis (analysis_method={analysis_method}):")
    for i, result in enumerate(cpei_results):
        if analysis_method == 2:  # CPEI - optimum_angle is string
            angle_text = result['optimum_angle']
            y_position = result['max_correlation']  # Use correlation value for y position
            print(f"  ✅ Well {i}: angle_text='{angle_text}', y_position={y_position}")
    
    print(f"\n🎉 All tests completed!")

if __name__ == "__main__":
    test_formatting_fixes()