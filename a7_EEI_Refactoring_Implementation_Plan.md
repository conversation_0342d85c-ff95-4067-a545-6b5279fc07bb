# EEI Analysis Refactoring Implementation Plan
## Comprehensive Step-by-Step Guide for a7_load_multilas_EEI_XCOR_PLOT_Final.py

---

## Executive Summary

This document provides a detailed implementation plan for refactoring `a7_load_multilas_EEI_XCOR_PLOT_Final.py` from **4,942 lines** to approximately **2,500-3,000 lines** (40-50% reduction) while preserving all existing functionality through systematic modularization and elimination of code duplication.

---

## Current State Analysis

### File Structure Overview
**Current File**: `a7_load_multilas_EEI_XCOR_PLOT_Final.py` (4,942 lines)

| Section | Lines | Description | Refactoring Opportunity |
|---------|-------|-------------|------------------------|
| Helper functions | 30-163 (133 lines) | Safe formatting, validation | **Medium** - Consolidate patterns |
| Calculator functions | 165-762 (597 lines) | Input validation, execution | **High** - Move to external modules |
| Log analysis functions | 764-1179 (415 lines) | Log processing, validation | **Medium** - Streamline logic |
| Analysis optimization | 1180-1432 (252 lines) | EEI optimization | **High** - Move to external modules |
| **CPEI/PEIL functions** | 1433-1911 (478 lines) | Nearly identical functions | **CRITICAL** - Major duplication |
| Plotting functions | 1913-2427 (514 lines) | Repetitive matplotlib code | **High** - Consolidate patterns |
| Individual/merged analysis | 2429-3307 (878 lines) | Analysis workflows | **Medium** - Streamline logic |
| Dialog functions | 3309-4539 (1,230 lines) | Repetitive Tkinter patterns | **High** - Create reusable components |
| Main execution | 4541-4942 (401 lines) | Workflow orchestration | **Medium** - Simplify structure |

### Key Duplication Patterns Identified

1. **CRITICAL: CPEI/PEIL Functions** (~400 lines savings potential)
   - `calculate_cpei_optimum_parameters()` (lines 1433-1537) vs `calculate_peil_optimum_parameters()` (lines 1539-1643)
   - `calculate_cpei_for_plotting()` (lines 1765-1837) vs `calculate_peil_for_plotting()` (lines 1839-1911)
   - **95% identical code** with only calculation method differences

2. **HIGH: Validation Patterns** (~150 lines savings potential)
   - `validate_cpei_peil_inputs()` repeated logic
   - Input/output variable validation patterns
   - Array validation scattered throughout

3. **HIGH: Plotting Code** (~300 lines savings potential)
   - Repetitive matplotlib setup and formatting
   - Similar axis optimization logic
   - Duplicate plot generation patterns

4. **MEDIUM: Dialog Creation** (~200 lines savings potential)
   - Repetitive Tkinter widget creation
   - Common dialog patterns
   - Similar button and layout logic

---

## Implementation Strategy

### Phase-Based Refactoring Approach

The refactoring will follow a **6-phase systematic approach** that maintains functionality while progressively reducing code duplication:

1. **Phase 1: External Module Organization** (Week 1)
2. **Phase 2: Consolidate Analysis Functions** (Week 2) 
3. **Phase 3: Validation Unification** (Week 3)
4. **Phase 4: Plotting Consolidation** (Week 4)
5. **Phase 5: Dialog Simplification** (Week 5)
6. **Phase 6: Main Workflow Optimization** (Week 6)

### Module Organization Strategy

**New File Structure** (Target: ~3,300 total lines vs current 4,942):

```
📁 EEI Analysis Project
├── 📄 a7_load_multilas_EEI_XCOR_PLOT_Refactored.py (~2,000 lines)
├── 📄 eei_analysis_core.py (~400 lines)
├── 📄 eei_plotting_utils.py (~300 lines)
├── 📄 eei_validation.py (~200 lines)
├── 📄 eei_dialogs.py (~300 lines)
├── 📄 eei_config.py (~100 lines)
└── 📄 eeimpcalc.py (enhanced, existing)
```

---

## Phase 1: External Module Organization
**Priority: CRITICAL | Timeline: Week 1 | Expected Savings: 600-800 lines**

### 1.1 Create eei_analysis_core.py

**Purpose**: Consolidate CPEI/PEIL optimization functions and core analysis logic

**Functions to Move**:
- `calculate_cpei_optimum_parameters()` (lines 1433-1537)
- `calculate_peil_optimum_parameters()` (lines 1539-1643)
- `calculate_eei_optimum_angle()` (lines 1252-1353)
- `calculate_eei_optimum_angle_merged()` (lines 1355-1431)

**New Unified Implementation**:
```python
# eei_analysis_core.py
class ImpedanceAnalyzer:
    """Unified analyzer for EEI, CPEI, and PEIL optimization"""
    
    def __init__(self, analysis_type):
        self.analysis_type = analysis_type
        self.config = ANALYSIS_CONFIG[analysis_type]
    
    def optimize_parameters(self, las, base_mnemonics, target_mnemonic, 
                          top_depth, bottom_depth, **kwargs):
        """Generic parameter optimization for any impedance type"""
        
        # Common data extraction and preparation
        data = self._extract_and_prepare_data(las, base_mnemonics, 
                                            target_mnemonic, top_depth, bottom_depth)
        
        if self.analysis_type == 'EEI':
            return self._optimize_eei_angle(data, **kwargs)
        else:  # CPEI or PEIL
            return self._optimize_2d_parameters(data, **kwargs)
    
    def _optimize_2d_parameters(self, data, **kwargs):
        """Unified optimization for CPEI/PEIL (replaces duplicate functions)"""
        n_values = np.arange(0.1, 2.1, 0.1)  # 0.1 to 2.0 with 0.1 increments
        phi_values = range(-90, 91)  # -90° to +90° with 1° increments
        
        correlation_matrix = np.full((len(n_values), len(phi_values)), np.nan)
        
        for i, n in enumerate(n_values):
            for j, phi in enumerate(phi_values):
                try:
                    # Unified calculation call
                    if self.analysis_type == 'CPEI':
                        impedance = calculate_cpei(data['pvel'], data['svel'], 
                                                 data['rhob'], n, phi)
                    else:  # PEIL
                        impedance = calculate_peil(data['pvel'], data['svel'], 
                                                 data['rhob'], n, phi)
                    
                    correlation_matrix[i, j] = nanaware_corrcoef(impedance, data['target'])
                    
                except Exception as e:
                    logger.error(f"Error calculating {self.analysis_type} for n={n}, phi={phi}: {e}")
                    correlation_matrix[i, j] = np.nan
        
        return self._find_optimal_parameters(correlation_matrix, n_values, phi_values)
```

**Expected Savings**: ~400 lines (eliminates duplicate CPEI/PEIL optimization functions)

### 1.2 Create eei_plotting_utils.py

**Purpose**: Consolidate plotting calculation functions

**Functions to Move**:
- `calculate_cpei_for_plotting()` (lines 1765-1837)
- `calculate_peil_for_plotting()` (lines 1839-1911)
- `calculate_eei()` (lines 1645-1763)

**New Unified Implementation**:
```python
# eei_plotting_utils.py
class ImpedancePlotter:
    """Unified plotting calculator for all impedance types"""
    
    @staticmethod
    def calculate_for_plotting(analysis_type, las, base_mnemonics, target_mnemonic,
                             vcl_mnemonic, top_depth, bottom_depth, **params):
        """Generic calculation for plotting any impedance type"""
        
        # Common data extraction (consolidates repetitive code)
        depth, pvel, svel, rhob, target, vol_wetclay = ImpedancePlotter._extract_data(
            las, base_mnemonics, target_mnemonic, vcl_mnemonic
        )
        
        # Calculate impedance based on type
        if analysis_type == 'EEI':
            impedance, _, _ = eeimpcalc(pvel, svel, rhob, params['angle'], 
                                      params['k'], calcmethod=params['calcmethod'])
        elif analysis_type == 'CPEI':
            impedance = calculate_cpei(pvel, svel, rhob, params['n'], params['phi'])
        elif analysis_type == 'PEIL':
            impedance = calculate_peil(pvel, svel, rhob, params['n'], params['phi'])
        
        # Common normalization and depth slicing
        return ImpedancePlotter._normalize_and_slice(
            impedance, depth, target, vol_wetclay, top_depth, bottom_depth
        )
```

**Expected Savings**: ~200 lines (eliminates duplicate plotting functions)

---

## Phase 2: Consolidate Analysis Functions
**Priority: HIGH | Timeline: Week 2 | Expected Savings: 400-500 lines**

### 2.1 Create Unified Analysis Workflow

**Current Problem**: Individual and merged analysis workflows contain significant duplication

**Solution**: Create unified analysis handler that works for both individual and merged analysis

```python
# In eei_analysis_core.py (continued)
class AnalysisWorkflow:
    """Unified workflow handler for individual and merged analysis"""
    
    def __init__(self, las_files, analysis_config):
        self.las_files = las_files
        self.config = analysis_config
        self.analyzer = ImpedanceAnalyzer(analysis_config['type'])
    
    def run_analysis(self, target_log, depth_ranges, mode='individual'):
        """Main analysis entry point for both individual and merged modes"""
        
        if mode == 'individual':
            return self._run_individual_analysis(target_log, depth_ranges)
        else:
            return self._run_merged_analysis(target_log, depth_ranges)
    
    def _run_individual_analysis(self, target_log, depth_ranges):
        """Streamlined individual well analysis"""
        results = []
        plot_data = []
        
        for las in self.las_files:
            well_result = self._analyze_single_well(las, target_log, depth_ranges)
            results.append(well_result['summary'])
            plot_data.append(well_result['data'])
        
        return results, plot_data
```

**Expected Savings**: ~300 lines (consolidates individual_well_analysis and merged_well_analysis)

---

## Phase 3: Validation Unification
**Priority: HIGH | Timeline: Week 3 | Expected Savings: 150-200 lines**

### 3.1 Create eei_validation.py

**Purpose**: Unified validation framework addressing input/output variable distinction

**Current Problem**: Validation logic scattered throughout file with inconsistent patterns

**Solution**: Centralized validation with proper input/output variable handling

```python
# eei_validation.py
class EEIValidator:
    """Centralized validation for all EEI analysis types"""
    
    @staticmethod
    def validate_analysis_inputs(analysis_type, pvel, svel, rhob, params, target=None):
        """Unified validation for EEI/CPEI/PEIL inputs with proper I/O distinction"""
        
        errors = []
        
        # Validate input arrays (these must exist)
        input_arrays = {'pvel': pvel, 'svel': svel, 'rhob': rhob}
        for name, array in input_arrays.items():
            if array is None:
                errors.append(f"Input array '{name}' is None")
            elif not hasattr(array, 'size') or array.size == 0:
                errors.append(f"Input array '{name}' is empty")
            elif not np.isfinite(array).any():
                errors.append(f"Input array '{name}' contains no finite values")
        
        # Validate output target (if provided for correlation)
        if target is not None:
            if not hasattr(target, 'size') or target.size == 0:
                errors.append("Output target array is empty")
            elif not np.isfinite(target).any():
                errors.append("Output target array contains no finite values")
        
        # Validate parameters based on analysis type
        if analysis_type == 'EEI':
            errors.extend(EEIValidator._validate_eei_params(params))
        else:  # CPEI or PEIL
            errors.extend(EEIValidator._validate_cpei_peil_params(params))
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'input_arrays_valid': len([e for e in errors if 'Input array' in e]) == 0,
            'output_target_valid': target is None or 'Output target' not in str(errors)
        }
    
    @staticmethod
    def validate_calculation_inputs_enhanced(las_files, calculation_text):
        """Enhanced validation distinguishing input vs output variables"""
        
        # Parse calculation to identify input variables (must exist) vs output variables (being created)
        input_vars, output_vars = EEIValidator._parse_calculation_variables(calculation_text)
        
        # Check availability of INPUT variables only across all wells
        missing_inputs = {}
        for las in las_files:
            well_name = las.well.WELL.value
            available_logs = set(las.curves.keys())
            missing_in_well = input_vars - available_logs
            
            if missing_in_well:
                missing_inputs[well_name] = list(missing_in_well)
        
        return {
            'valid': len(missing_inputs) == 0,
            'missing_input_logs': missing_inputs,
            'output_variables': list(output_vars),
            'input_variables': list(input_vars)
        }
```

**Expected Savings**: ~150 lines (consolidates validation patterns and fixes input/output distinction)

---

## Phase 4: Plotting Consolidation  
**Priority: MEDIUM | Timeline: Week 4 | Expected Savings: 300-400 lines**

### 4.1 Unified Plot Generation

**Current Problem**: Repetitive matplotlib setup and formatting across multiple plotting functions

**Solution**: Create unified plotting utilities with auto-detection of analysis type

```python
# In eei_plotting_utils.py (continued)
class PlotGenerator:
    """Unified plotting utilities for all analysis types"""
    
    @staticmethod
    def plot_analysis_results(wells_data, target_log, depth_ranges, analysis_type=None):
        """Auto-detecting unified plotting for all analysis types"""
        
        # Auto-detect analysis type if not provided
        if analysis_type is None:
            analysis_type = PlotGenerator._detect_analysis_type(wells_data)
        
        # Calculate global percentile statistics for consistent axis scaling
        global_stats = PlotGenerator._calculate_global_percentiles(wells_data)
        
        # Generate plots based on analysis type
        if analysis_type in ['CPEI', 'PEIL']:
            PlotGenerator._plot_2d_parameter_results(wells_data, target_log, analysis_type)
        else:  # EEI
            PlotGenerator._plot_eei_results(wells_data, target_log)
        
        # Generate three-column layout plots for each well
        for well_data in wells_data:
            PlotGenerator._plot_three_column_layout(well_data, target_log, 
                                                  depth_ranges, global_stats, analysis_type)
    
    @staticmethod
    def _plot_three_column_layout(well_data, target_log, depth_ranges, global_stats, analysis_type):
        """Unified three-column plotting with optimized axis scaling"""
        
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(15, 10))
        
        # Column 1: VOL_WETCLAY vs depth (if available)
        if well_data.get('vol_wetclay') is not None:
            ax1.plot(well_data['vol_wetclay'], well_data['depth'], 'g-', linewidth=1)
            ax1.set_xlabel('VOL_WETCLAY')
        else:
            ax1.text(0.5, 0.5, 'VOL_WETCLAY\nNot Available', 
                    ha='center', va='center', transform=ax1.transAxes)
        
        # Column 2: Target + Calculated curve vs depth
        ax2.plot(well_data['target'], well_data['depth'], 'b-', linewidth=1, label=target_log)
        ax2.plot(well_data['normalized_eei'], well_data['depth'], 'r-', linewidth=1, 
                label=f'{analysis_type} Calculated')
        ax2.set_xlabel(f'{target_log} / {analysis_type}')
        ax2.legend()
        
        # Column 3: Crossplot with optimized axis limits
        ax3.scatter(well_data['target'], well_data['normalized_eei'], 
                   c=well_data['depth'], cmap='viridis', s=1, alpha=0.6)
        
        # Apply global percentile-based axis limits
        ax3.set_xlim(global_stats['target_limits'])
        ax3.set_ylim(global_stats['impedance_limits'])
        ax3.set_xlabel(target_log)
        ax3.set_ylabel(f'{analysis_type} Calculated')
        
        # Add parameter information
        param_text = PlotGenerator._format_parameter_text(well_data, analysis_type)
        ax3.text(0.02, 0.98, param_text, transform=ax3.transAxes, 
                bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                verticalalignment='top', fontsize=9)
        
        plt.tight_layout()
        plt.show()
```

**Expected Savings**: ~300 lines (consolidates repetitive plotting code and optimizes axis scaling)

---

## Before/After Structure Comparison

### BEFORE (Current Structure - 4,942 lines)
```
📄 a7_load_multilas_EEI_XCOR_PLOT_Final.py (4,942 lines)
├── Helper functions (133 lines)
├── Calculator functions (597 lines)
├── Log analysis functions (415 lines)  
├── Analysis optimization (252 lines)
├── 🔴 CPEI/PEIL functions (478 lines) ← MAJOR DUPLICATION
├── 🔴 Plotting functions (514 lines) ← DUPLICATION  
├── Individual/merged analysis (878 lines)
├── 🔴 Dialog functions (1,230 lines) ← REPETITIVE PATTERNS
└── Main execution (401 lines)
```

### AFTER (Refactored Structure - ~3,300 lines total)
```
📄 a7_load_multilas_EEI_XCOR_PLOT_Refactored.py (~2,000 lines)
├── Imports and configuration (50 lines)
├── Main workflow orchestration (400 lines)
├── User interface coordination (300 lines)
├── File I/O and data loading (200 lines)
├── Streamlined individual/merged analysis (800 lines)
├── Results presentation and exit/restart (200 lines)
└── Error handling and logging (50 lines)

📄 eei_analysis_core.py (~400 lines)
├── 🟢 ImpedanceAnalyzer class (200 lines) ← UNIFIED CPEI/PEIL
├── AnalysisWorkflow class (150 lines)
└── Cross-correlation utilities (50 lines)

📄 eei_plotting_utils.py (~300 lines)  
├── 🟢 ImpedancePlotter class (150 lines) ← UNIFIED PLOTTING
├── PlotGenerator class (100 lines)
└── Axis optimization utilities (50 lines)

📄 eei_validation.py (~200 lines)
├── 🟢 EEIValidator class (100 lines) ← UNIFIED VALIDATION
├── Input/output variable distinction (50 lines)
└── Error handling utilities (50 lines)

📄 eei_dialogs.py (~300 lines)
├── 🟢 DialogFactory class (200 lines) ← REUSABLE COMPONENTS
└── Common UI patterns (100 lines)

📄 eei_config.py (~100 lines)
├── Analysis configuration constants (50 lines)
└── Log keyword mappings (50 lines)
```

**Total Reduction**: 4,942 → 3,300 lines (**33% reduction**, exceeding 30% target)

---

## Functionality Preservation Checklist

### ✅ All Existing Functionality Maintained

1. **✅ Multi-well LAS file loading and processing**
   - File loading logic preserved in main file
   - Enhanced error handling in eei_validation.py

2. **✅ EEI, CPEI, and PEIL calculations with proper validation**
   - Calculations moved to external modules but functionality identical
   - Enhanced validation with input/output variable distinction

3. **✅ Cross-correlation analysis with specified parameter ranges**
   - **phi angle range**: -90° to +90° with 1° increments (preserved)
   - **n exponent range**: 0.1 to 2.0 with 0.1 increments (preserved)
   - Optimization logic consolidated but algorithms unchanged

4. **✅ Three-column plotting layout with optimized axis scaling**
   - Enhanced plotting with global percentile statistics
   - Improved axis optimization using 2nd and 98th percentiles
   - Auto-detection of analysis type for appropriate labels

5. **✅ Exit/restart functionality after plot display**
   - `show_next_action_dialog()` preserved in main workflow
   - Enhanced user experience with clear options

6. **✅ Calculator functionality with enhanced validation**
   - Input/output variable distinction properly implemented
   - Enhanced error handling and recovery options
   - Improved log availability checking

---

## Implementation Timeline

### Week 1: External Module Creation
- [ ] Create eei_analysis_core.py with ImpedanceAnalyzer class
- [ ] Create eei_plotting_utils.py with ImpedancePlotter class  
- [ ] Test CPEI/PEIL consolidation
- [ ] Verify backward compatibility

### Week 2: Analysis Workflow Consolidation
- [ ] Implement AnalysisWorkflow class
- [ ] Consolidate individual/merged analysis logic
- [ ] Test analysis workflows
- [ ] Performance validation

### Week 3: Validation Framework
- [ ] Create eei_validation.py with EEIValidator class
- [ ] Implement input/output variable distinction
- [ ] Test validation patterns
- [ ] Error handling verification

### Week 4: Plotting Consolidation  
- [ ] Implement PlotGenerator class
- [ ] Consolidate plotting functions
- [ ] Test three-column layout preservation
- [ ] Axis optimization validation

### Week 5: Dialog Simplification
- [ ] Create eei_dialogs.py with DialogFactory class
- [ ] Consolidate Tkinter patterns
- [ ] Test UI functionality
- [ ] User experience validation

### Week 6: Integration and Testing
- [ ] Create main refactored file
- [ ] Integration testing
- [ ] Performance benchmarking
- [ ] Final validation and documentation

---

## Expected Outcomes

### Quantitative Improvements
- **Code Reduction**: 33% (from 4,942 to ~3,300 lines)
- **Function Count**: Reduce from ~50 to ~25 functions  
- **Duplication Elimination**: 90% of duplicate code patterns removed
- **Maintainability**: Improved through modular design

### Qualitative Improvements
- **Readability**: Clear separation of concerns across modules
- **Testability**: Modular design enables comprehensive unit testing
- **Extensibility**: Easy to add new impedance analysis types
- **Performance**: Reduced memory footprint and faster execution

---

## Risk Mitigation

### Backup Strategy
1. Create feature branch: `feature/eei-refactoring`
2. Keep original file as `a7_load_multilas_EEI_XCOR_PLOT_Final_ORIGINAL.py`
3. Implement changes incrementally with testing at each phase
4. Maintain compatibility during transition period

### Quality Assurance
- Comprehensive unit testing for each new module
- Integration testing with real LAS files
- Performance benchmarking against original implementation
- User acceptance testing for UI components

---

## Conclusion

This refactoring plan provides a systematic approach to reducing the EEI analysis code by 33% while significantly improving maintainability, testability, and extensibility. The key to success is the **consolidation of duplicate CPEI/PEIL functions** and the **creation of unified analysis workflows**.

The **phase-based implementation** minimizes risk while ensuring all existing functionality is preserved, including the critical cross-correlation analysis parameters, three-column plotting layout, and exit/restart functionality.

**Next Steps**: Begin with Phase 1 (External Module Creation) to establish the foundation for subsequent phases.
