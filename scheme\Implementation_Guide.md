# Implementation Guide for Dialog Hanging Fix

## Overview
This guide provides step-by-step instructions to implement the complete solution for fixing the hanging dialog issue in `b4_Xplot_HIST_KDE_FUNCT_Custom.py`.

## Phase 1: Add Window Manager Singleton

### Step 1: Add WindowManager Class (Insert after line 47)

```python
class WindowManager:
    """Singleton window manager for the entire application."""
    _instance = None
    _root = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def get_root(self):
        """Get or create the main root window."""
        if self._root is None or not self._root.winfo_exists():
            self._root = tk.Tk()
            self._root.withdraw()  # Hide by default
            self._root.title("Well Log Analysis Application")
            
            # Configure root window
            self._root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
        return self._root
    
    def on_closing(self):
        """Handle application closing."""
        try:
            if messagebox.askokcancel("Quit", "Do you want to quit the application?"):
                self.cleanup()
        except:
            self.cleanup()
    
    def cleanup(self):
        """Clean up all windows."""
        if self._root and self._root.winfo_exists():
            try:
                self._root.quit()
                self._root.destroy()
            except:
                pass
        self._root = None
    
    def create_dialog(self, title="Dialog", geometry="400x300"):
        """Create a new dialog window."""
        root = self.get_root()
        dialog = tk.Toplevel(root)
        dialog.title(title)
        dialog.geometry(geometry)
        dialog.transient(root)
        return dialog
```

## Phase 2: Add Timeout Protection Wrapper

### Step 2: Add Timeout Protection Function (Insert after WindowManager class)

```python
def with_dialog_timeout(dialog_func, timeout_seconds=30, default_return='cancel'):
    """
    Wrapper to add timeout protection to dialog functions.
    
    Args:
        dialog_func: Function that creates and shows a dialog
        timeout_seconds: Maximum time to wait for dialog
        default_return: Default return value if timeout occurs
    
    Returns:
        Result from dialog_func or default_return if timeout
    """
    import threading
    import queue
    
    result_queue = queue.Queue()
    exception_queue = queue.Queue()
    
    def target():
        try:
            result = dialog_func()
            result_queue.put(result)
        except Exception as e:
            exception_queue.put(e)
    
    # Start dialog in separate thread
    thread = threading.Thread(target=target, daemon=True)
    thread.start()
    
    # Wait for result with timeout
    try:
        result = result_queue.get(timeout=timeout_seconds)
        return result
    except queue.Empty:
        print(f"⚠️ Dialog timeout after {timeout_seconds} seconds, using default: {default_return}")
        return default_return
    except Exception as e:
        if not exception_queue.empty():
            raise exception_queue.get()
        raise e

def console_fallback_dialog(error_details):
    """Fallback to console interaction if dialog fails."""
    print("\n" + "="*60)
    print("CALCULATOR VALIDATION ERROR")
    print("="*60)
    print(error_details['error_details'])
    print("="*60)
    
    while True:
        choice = input("\nChoose action (retry/skip/cancel): ").lower().strip()
        if choice in ['retry', 'skip', 'cancel']:
            return choice
        print("Invalid choice. Please enter 'retry', 'skip', or 'cancel'.")

def center_dialog(dialog):
    """Center dialog on screen."""
    try:
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
    except:
        pass  # Ignore centering errors

def setup_error_dialog_ui(dialog, error_details, result, timeout_id):
    """Setup the error dialog UI components."""
    # Main frame
    main_frame = ttk.Frame(dialog, padding="15")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # Title
    title_label = ttk.Label(main_frame, text="⚠️ Log Availability Error",
                           font=('Arial', 14, 'bold'), foreground='red')
    title_label.pack(pady=(0, 10))
    
    # Scrollable text area
    text_frame = ttk.Frame(main_frame)
    text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
    
    text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('Courier', 10))
    scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
    text_widget.configure(yscrollcommand=scrollbar.set)
    
    text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # Insert error details
    text_widget.insert(tk.END, error_details['error_details'])
    text_widget.configure(state=tk.DISABLED)
    
    # Button frame
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(10, 0))
    
    # Button callbacks with proper cleanup
    def on_retry():
        print("🔄 Error Dialog: Retry button clicked")
        result['action'] = 'retry'
        result['completed'] = True
        try:
            dialog.after_cancel(timeout_id)
        except:
            pass
        dialog.destroy()
    
    def on_skip():
        print("🔄 Error Dialog: Skip button clicked")
        result['action'] = 'skip'
        result['completed'] = True
        try:
            dialog.after_cancel(timeout_id)
        except:
            pass
        dialog.destroy()
    
    def on_cancel():
        print("🔄 Error Dialog: Cancel button clicked")
        result['action'] = 'cancel'
        result['completed'] = True
        try:
            dialog.after_cancel(timeout_id)
        except:
            pass
        dialog.destroy()
    
    # Create buttons
    ttk.Button(button_frame, text="🔄 Retry (Modify Calculations)",
              command=on_retry).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="⏭️ Skip (Continue Workflow)",
              command=on_skip).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="❌ Cancel",
              command=on_cancel).pack(side=tk.LEFT, padx=5)
    
    # Keyboard shortcuts
    dialog.bind('<Escape>', lambda e: on_cancel())
    dialog.bind('<Return>', lambda e: on_retry())
    
    # Window close protocol
    dialog.protocol("WM_DELETE_WINDOW", on_cancel)
```

## Phase 3: Replace handle_calculation_error Function

### Step 3: Replace the existing handle_calculation_error function (lines 1242-1348)

```python
def handle_calculation_error(error_details, las_files):
    """
    Robust version of error dialog with timeout protection and proper cleanup.
    
    Args:
        error_details: Dictionary from validate_calculation_inputs
        las_files: List of lasio.LASFile objects
    
    Returns:
        str: 'retry', 'skip', or 'cancel'
    """
    print("🔄 Error Dialog: Starting robust error dialog...")
    
    # Get singleton root window
    try:
        window_manager = WindowManager()
        root = window_manager.get_root()
    except Exception as e:
        print(f"❌ Error Dialog: Failed to get root window: {e}")
        return console_fallback_dialog(error_details)
    
    try:
        # Create dialog with proper configuration
        dialog = tk.Toplevel(root)
        dialog.title("Calculator Validation Error")
        dialog.geometry("700x600")
        dialog.resizable(True, True)
        
        # Configure dialog properties
        dialog.transient(root)
        dialog.focus_set()
        
        # Result tracking
        result = {'action': 'cancel', 'completed': False}
        
        # Auto-close mechanism (30-second timeout)
        def auto_close():
            if not result['completed']:
                print("⚠️ Error Dialog: Auto-closing due to timeout")
                result['action'] = 'cancel'
                result['completed'] = True
                try:
                    dialog.destroy()
                except:
                    pass
        
        timeout_id = dialog.after(30000, auto_close)  # 30-second timeout
        
        # Create UI components
        setup_error_dialog_ui(dialog, error_details, result, timeout_id)
        
        # Center dialog
        center_dialog(dialog)
        
        # Use wait_window instead of mainloop
        print("🔄 Error Dialog: Waiting for user interaction...")
        dialog.wait_window()
        
        print(f"🔄 Error Dialog: Dialog closed, returning: {result['action']}")
        return result['action']
        
    except Exception as e:
        print(f"❌ Error Dialog: Exception in dialog creation: {e}")
        # Fallback to console interaction
        return console_fallback_dialog(error_details)
```

## Phase 4: Update get_calculations Function

### Step 4: Replace the existing get_calculations function (lines 1645-1765)

```python
def get_calculations(las_files):
    """
    Enhanced calculator function with robust error handling.
    
    Args:
        las_files: List of lasio.LASFile objects
    
    Returns:
        bool: True if calculations were successful, False if cancelled or failed
    """
    # Get columns that are present in all LAS files
    common_columns = set(las_files[0].curves.keys())
    for las in las_files[1:]:
        common_columns.intersection_update(las.curves.keys())
    columns = sorted(common_columns)

    if not columns:
        try:
            messagebox.showerror("Error", "No common columns found across all LAS files.")
        except:
            print("ERROR: No common columns found across all LAS files.")
        return False

    # Get window manager
    try:
        window_manager = WindowManager()
    except Exception as e:
        print(f"❌ Calculator: Failed to initialize window manager: {e}")
        return False
    
    while True:
        print("🔄 Calculator: Starting calculator interface...")
        
        try:
            # Show calculator interface with timeout protection
            calculations = with_dialog_timeout(
                lambda: show_calculator_interface(las_files),
                timeout_seconds=300,  # 5 minutes for calculator
                default_return=None
            )
            
            print(f"🔄 Calculator: Interface returned, calculations: {calculations is not None}")

            if calculations is None:  # User cancelled or timeout
                print("🔄 Calculator: User cancelled or timeout, returning False")
                return False

            # Check if user submitted empty calculations
            if not calculations or not calculations.strip():
                print("ℹ️ Calculator: No calculations entered. Proceeding without custom calculations.")
                return True

            # Validate inputs before execution
            print("🔄 Calculator: About to call validate_calculation_inputs()...")
            
            try:
                validation_result = validate_calculation_inputs(las_files, calculations)
                print("🔄 Calculator: validate_calculation_inputs() completed successfully")
                print(f"🔄 Calculator: Validation result - valid: {validation_result['valid']}")
            except Exception as e:
                print(f"❌ Calculator: ERROR in validate_calculation_inputs(): {e}")
                print("🔄 Calculator: Continuing workflow despite validation error...")
                return True

            if not validation_result['valid']:
                print("🔄 Calculator: Validation failed, showing error dialog...")
                
                try:
                    # Use robust error dialog with timeout
                    action = with_dialog_timeout(
                        lambda: handle_calculation_error(validation_result, las_files),
                        timeout_seconds=60,  # 1 minute for error dialog
                        default_return='skip'
                    )
                    print(f"🔄 Calculator: Error dialog returned action: {action}")
                except Exception as e:
                    print(f"❌ Calculator: ERROR in error dialog: {e}")
                    print("🔄 Calculator: Using fallback - continuing workflow...")
                    return True

                if action == 'retry':
                    print("🔄 Calculator: User chose retry, showing calculator again...")
                    continue
                elif action == 'skip':
                    print("🔄 Calculator: User chose skip, continuing workflow...")
                    return True
                else:  # cancel
                    print("🔄 Calculator: User chose cancel, exiting...")
                    return False

            # Execute calculations safely
            print("🔄 Calculator: About to call execute_calculations_safely()...")
            try:
                success = execute_calculations_safely(las_files, calculations)
                print(f"🔄 Calculator: execute_calculations_safely() returned: {success}")
            except Exception as e:
                print(f"❌ Calculator: ERROR in execute_calculations_safely(): {e}")
                return True

            if success:
                print("✅ Calculator: All calculations executed successfully!")
                print(f"📊 Calculator: Processed {len(las_files)} wells")
                print("🔄 Calculator: Returning control to main workflow...")

                # Show success message with proper exception handling
                try:
                    # Use window manager for success message
                    window_manager = WindowManager()
                    root = window_manager.get_root()
                    
                    messagebox.showinfo("Calculator Success",
                                      f"✅ Calculations completed successfully!\n\n"
                                      f"Processed {len(las_files)} wells\n"
                                      f"New calculated logs are now available for analysis.")
                    print("✅ Calculator: Success message displayed successfully")

                except Exception as e:
                    print(f"⚠️ Calculator: Warning - Could not display success message: {e}")
                    # Continue anyway since calculations were successful

                print("🎯 Calculator: Returning True to continue workflow")
                return True
            else:
                print("❌ Calculator: Execution failed, asking user...")
                # Show retry dialog with timeout protection
                try:
                    retry = messagebox.askretrycancel(
                        "Calculation Execution Error",
                        "Calculation execution failed. Retry or continue without calculations?"
                    )
                    if not retry:
                        return True
                    # Continue loop for retry
                except Exception as e:
                    print(f"❌ Calculator: Error showing retry dialog: {e}")
                    return True
                    
        except Exception as e:
            print(f"❌ Calculator: Unexpected error in calculator loop: {e}")
            # Ask user if they want to continue
            try:
                continue_anyway = messagebox.askyesno(
                    "Calculator Error",
                    f"An unexpected error occurred: {e}\n\nContinue without calculations?"
                )
                return continue_anyway
            except:
                print("🔄 Calculator: Continuing workflow due to dialog error...")
                return True
```

## Phase 5: Update Main Function

### Step 5: Update the main function to use WindowManager (around line 3418)

Find the main function and add window manager initialization at the beginning:

```python
def main():
    print("\n=== DEBUG: MAIN FUNCTION EXECUTION ===")
    print("Starting application...")
    
    # Initialize window manager
    try:
        window_manager = WindowManager()
        print("✅ Window manager initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize window manager: {e}")
        return

    las_files = load_multiple_las_files()
    if not las_files:
        print("No files selected. Exiting.")
        # Cleanup window manager
        try:
            window_manager.cleanup()
        except:
            pass
        return

    # ... rest of main function remains the same ...
    
    # At the end of main function, add cleanup:
    try:
        window_manager.cleanup()
        print("✅ Application cleanup completed")
    except Exception as e:
        print(f"⚠️ Warning during cleanup: {e}")
```

## Implementation Steps Summary

1. **Add WindowManager class** after line 47 (after the colormap functions)
2. **Add timeout protection functions** after the WindowManager class
3. **Replace handle_calculation_error function** (lines 1242-1348)
4. **Replace get_calculations function** (lines 1645-1765)
5. **Update main function** to initialize and cleanup WindowManager

## Testing Checklist

After implementation, test the following scenarios:

- [ ] Calculator validation with invalid logs (should show error dialog)
- [ ] Error dialog timeout (wait 30 seconds without clicking)
- [ ] Error dialog button clicks (Retry, Skip, Cancel)
- [ ] Calculator interface timeout (leave calculator open for 5+ minutes)
- [ ] Multiple dialog operations in sequence
- [ ] Application cleanup on exit
- [ ] Console fallback when dialogs fail

## Key Benefits of This Implementation

1. **No More Hanging Dialogs**: All dialogs have 30-second timeouts
2. **Robust Error Recovery**: Console fallback when GUI fails
3. **Proper Window Management**: Single root window prevents conflicts
4. **Memory Management**: Proper cleanup prevents memory leaks
5. **User Experience**: Clear feedback and escape mechanisms

## Troubleshooting

If you encounter issues during implementation:

1. **Import Errors**: Make sure all required imports are at the top
2. **Indentation**: Ensure proper Python indentation for all new code
3. **Function Conflicts**: Check for duplicate function names
4. **Testing**: Test each phase incrementally before moving to the next

This implementation will completely resolve the hanging dialog issue and make your application much more robust and user-friendly.