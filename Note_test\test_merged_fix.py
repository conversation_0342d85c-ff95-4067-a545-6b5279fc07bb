#!/usr/bin/env python3
"""
Simple test script to verify the NoneType format string fix for merged analysis.
"""

def safe_format_float(value, precision=4, default="N/A"):
    """Safely format a float value with proper None handling."""
    try:
        if value is None:
            return default
        if isinstance(value, (int, float)):
            return f"{float(value):.{precision}f}"
        else:
            return default
    except (ValueError, TypeError):
        return default

def test_fix():
    """Test the fix for NoneType format string errors."""
    
    print("Testing Summary Plotting NoneType Format String Fix")
    print("=" * 50)
    
    # Test case that would cause the error
    result = {
        'well_name': 'Well-1',
        'optimum_angle': 'n=1.1, phi=-24 degrees',
        'max_correlation': None,  # This causes the error
        'top_depth': 1000.0,
        'bottom_depth': None      # This also causes the error
    }
    
    print("Test case: CPEI result with None values")
    print(f"  max_correlation: {result['max_correlation']}")
    print(f"  top_depth: {result['top_depth']}")
    print(f"  bottom_depth: {result['bottom_depth']}")
    print()
    
    # Test the old problematic format
    print("Old format (would cause error):")
    try:
        old_format = f"{result['optimum_angle']}\n{result['max_correlation']:.3f}\n{result['top_depth']:.0f}-{result['bottom_depth']:.0f}"
        print(f"  Unexpected success: {old_format}")
    except (TypeError, ValueError) as e:
        print(f"  Expected error: {type(e).__name__}: {str(e)}")
    
    # Test the new safe format
    print("\nNew safe format:")
    try:
        angle_text = result['optimum_angle']
        safe_format = f"{angle_text}\n{safe_format_float(result['max_correlation'], precision=3, default='N/A')}\n{safe_format_float(result['top_depth'], precision=0, default='N/A')}-{safe_format_float(result['bottom_depth'], precision=0, default='N/A')}"
        print(f"  Success: {safe_format}")
    except Exception as e:
        print(f"  Unexpected error: {type(e).__name__}: {str(e)}")
    
    print("\nFix Verification:")
    print("- None values are converted to 'N/A' instead of causing errors")
    print("- Normal numeric values are formatted correctly")
    print("- The fix prevents 'unsupported format string passed to NoneType.__format__' errors")

if __name__ == "__main__":
    test_fix()
    print("\nTEST COMPLETED!")