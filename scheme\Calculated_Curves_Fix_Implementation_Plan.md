# Calculated Curves Fix Implementation Plan

## Overview
This document outlines the fixes for two related issues in `b4_Xplot_HIST_KDE_FUNCT_Custom.py`:
1. Calculated curves incorrectly appearing in the "Original Logs" group
2. Calculator output curves not appearing in dropdown menus

## Root Cause Analysis

### Issue 1: Incorrect Grouping
- **Location**: `create_column_display_list()` function (line 2132)
- **Problem**: All curves from LAS files are being added to `original_columns_set`, including those marked as calculated
- **Impact**: Calculated curves appear in both "Original Logs" and "Calculated Curves" sections

### Issue 2: Missing Calculator Output
- **Location**: Column analysis happens before calculator execution
- **Problem**: The dropdown menus are populated before new calculated curves exist
- **Impact**: Newly calculated curves don't appear in the selection dropdowns

## Detailed Fix Implementation

### Fix 1: Correct Column Categorization Logic

**Location**: Lines 2113-2130 in `get_column_names_and_depths()`

**Current Code**:
```python
# Identify calculated vs original columns using metadata
calculated_columns_set = set()
original_columns_set = set()

for las_file_obj in las_files:
    well_calculated = getattr(las_file_obj, '_calculated_curves', set())
    calculated_columns_set.update(well_calculated)
    
    # All other curves are original
    for curve_name in las_file_obj.curves.keys():
        if curve_name not in well_calculated:
            original_columns_set.add(curve_name)
```

**Fixed Code**:
```python
# Identify calculated vs original columns using metadata
calculated_columns_set = set()
original_columns_set = set()

# Debug output
print("\n=== DEBUG: COLUMN CATEGORIZATION ===")

for las_file_obj in las_files:
    well_name = las_file_obj.well.WELL.value
    well_calculated = getattr(las_file_obj, '_calculated_curves', set())
    
    print(f"Well {well_name}:")
    print(f"  Total curves: {len(las_file_obj.curves)}")
    print(f"  Calculated curves metadata: {well_calculated}")
    
    # Update the calculated set
    calculated_columns_set.update(well_calculated)
    
    # Only add curves that are NOT in calculated set to original set
    for curve_name in las_file_obj.curves.keys():
        if curve_name not in well_calculated:
            original_columns_set.add(curve_name)
        else:
            print(f"  Curve '{curve_name}' identified as calculated")

print(f"\nTotal original curves: {len(original_columns_set)}")
print(f"Total calculated curves: {len(calculated_columns_set)}")
print("================================\n")
```

### Fix 2: Enhance Column Display List Creation

**Location**: Lines 2132-2186 in `create_column_display_list()`

**Key Changes**:
```python
def create_column_display_list():
    """Create column list with proper grouping of original vs calculated curves."""
    display_columns = []
    
    # Debug output to verify separation
    print("DEBUG: create_column_display_list called")
    print(f"  Original columns ({len(original_columns)}): {sorted(original_columns)[:5]}...")
    print(f"  Calculated columns ({len(calculated_columns)}): {sorted(calculated_columns)}")
    
    # Section 1: Original Logs (explicitly exclude calculated curves)
    # Filter to ensure no calculated curves appear in original section
    original_common_display = [col for col in sorted(original_columns) 
                              if col in common_columns and col not in calculated_columns]
    original_partial_display = [col for col in sorted(original_columns)
                               if col in partial_columns and col not in common_columns 
                               and col not in calculated_columns]
    
    if original_common_display or original_partial_display:
        display_columns.append("--- Original Logs ---")
        
        for col in original_common_display:
            display_columns.append(f"✅ {col}")
        
        for col in original_partial_display:
            if col in partial_columns and 'count' in partial_columns[col]:
                well_count = partial_columns[col]['count']
                display_columns.append(f"⚠️ {col} ({well_count}/{total_wells} wells)")
            else:
                display_columns.append(f"⚠️ {col} (partial availability)")
    
    # Section 2: Calculated Curves (ensure these are actually calculated)
    # Only show curves that are explicitly in the calculated set
    calc_common_display = [col for col in sorted(calculated_columns) 
                          if col in common_columns]
    calc_partial_display = [col for col in sorted(calculated_columns)
                           if col in partial_columns and col not in common_columns]
    
    if calc_common_display or calc_partial_display:
        display_columns.append("--- Calculated Curves ---")
        
        for col in calc_common_display:
            display_columns.append(f"🧮 {col} (calculated)")
        
        for col in calc_partial_display:
            if col in partial_columns and 'count' in partial_columns[col]:
                well_count = partial_columns[col]['count']
                display_columns.append(f"⚠️🧮 {col} (calculated, {well_count}/{total_wells} wells)")
            else:
                display_columns.append(f"⚠️🧮 {col} (calculated, partial availability)")
    
    print(f"DEBUG: Display list created with {len(display_columns)} items")
    return display_columns
```

### Fix 3: Verify Calculated Curves Persistence

**Location**: In `execute_calculations_safely()` after line 1697

**Add Verification**:
```python
# After adding the curve, verify it's actually in the LAS object
if var_name in las.curves.keys():
    print(f"✅ VERIFIED: Curve '{var_name}' successfully added to {well_name}")
    print(f"   Curve data length: {len(las[var_name].data)}")
    print(f"   Curve unit: {las[var_name].unit}")
else:
    print(f"❌ ERROR: Curve '{var_name}' was NOT added to {well_name}")
```

### Fix 4: Add Post-Calculation Verification

**Location**: In `main()` function after line 3922

**Add Debug Output**:
```python
if success:
    print("\n=== POST-CALCULATION VERIFICATION ===")
    all_calculated_curves = set()
    
    for i, las in enumerate(las_files):
        well_name = las.well.WELL.value
        print(f"\nWell {well_name}:")
        print(f"  Total curves: {len(las.curves)}")
        print(f"  All curve names: {list(las.curves.keys())}")
        
        if hasattr(las, '_calculated_curves'):
            calc_curves = las._calculated_curves
            print(f"  Calculated curves metadata: {calc_curves}")
            all_calculated_curves.update(calc_curves)
            
            # Verify each calculated curve exists
            for curve in calc_curves:
                if curve in las.curves:
                    print(f"    ✅ '{curve}' exists in LAS file")
                else:
                    print(f"    ❌ '{curve}' NOT FOUND in LAS file")
        else:
            print(f"  No _calculated_curves metadata found")
    
    print(f"\nTotal unique calculated curves across all wells: {len(all_calculated_curves)}")
    print(f"Calculated curves: {sorted(all_calculated_curves)}")
    print("=====================================\n")
```

## Testing Plan

1. **Test Column Categorization**:
   - Run the script and check console output for column categorization debug messages
   - Verify that calculated curves only appear in the "Calculated Curves" section

2. **Test Calculator Integration**:
   - Add a simple calculation (e.g., `TEST_CALC = DT * 2`)
   - Verify the new curve appears in the dropdown
   - Check that it's in the "Calculated Curves" section

3. **Test Persistence**:
   - After calculations, verify curves are accessible in subsequent dialogs
   - Check that the `_calculated_curves` metadata is preserved

## Implementation Order

1. **Phase 1**: Implement Fix 1 (Column Categorization)
   - Update lines 2113-2130
   - Add debug output
   - Test with existing calculated curves

2. **Phase 2**: Implement Fix 2 (Display List)
   - Update `create_column_display_list()` function
   - Ensure proper filtering
   - Test dropdown display

3. **Phase 3**: Implement Fix 3 & 4 (Verification)
   - Add verification in `execute_calculations_safely()`
   - Add post-calculation checks in `main()`
   - Verify end-to-end workflow

## Expected Outcomes

After implementing these fixes:
1. Calculated curves will only appear in the "Calculated Curves" section
2. Original logs will only appear in the "Original Logs" section
3. Newly calculated curves will be immediately available in dropdowns
4. Clear debug output will help diagnose any remaining issues

## Rollback Plan

If issues arise:
1. The changes are isolated to specific functions
2. Original functionality can be restored by reverting the column categorization logic
3. Debug output can be commented out once issues are resolved