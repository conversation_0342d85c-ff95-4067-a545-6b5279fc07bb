# -*- coding: utf-8 -*-
"""
Created on Thu Aug 29 10:31:02 2024

@author: devri.agustianto
"""

import numpy as np

def eeimpcalc(pvel, svel, dens, angle, kvalue, calcmethod=1):
    sdt = angle  # angle is in degrees
    k = kvalue

    # Original EEI
    if calcmethod == 1:
        # Original calculation method
        px = np.cos(np.deg2rad(sdt)) + np.sin(np.deg2rad(sdt))
        qx = -8 * k * np.sin(np.deg2rad(sdt))
        rx = np.cos(np.deg2rad(sdt)) - 4 * k * np.sin(np.deg2rad(sdt))

        ao = np.nanmean(pvel)
        bo = np.nanmean(svel)
        co = np.nanmean(dens)

        scf = (ao * co) / ((ao**px) * (bo**qx) * (co**rx))
        EEI = scf * ((pvel**px) * (svel**qx) * (dens**rx))
    
    # AI, SI, densitas EEI formulation
    elif calcmethod == 2:
        # New calculation method
        # Correct definitions for px, qx, and rx
        px = np.cos(np.deg2rad(sdt)) + np.sin(np.deg2rad(sdt))
        qx = -8 * k * np.sin(np.deg2rad(sdt))
        rx = np.sin(np.deg2rad(sdt)) * (4 * k - 1)

        AI = pvel * dens
        SI = svel * dens

        ao = np.nanmean(pvel)
        bo = np.nanmean(svel)
        co = np.nanmean(dens)
        AIo = np.nanmean(AI)
        SIo = np.nanmean(SI)
        
        scf = (ao * co) / ((AIo**px) * (SIo**qx) * (co**rx))
        
        # Using the correct equation: EEI(x) = AI^(cos(x)+sin(x)) ⋅ SI^(−8ksin(x)) ⋅ ρ^(sin(x)(4k−1))
        EEI = scf * ((AI**px) * (SI**qx) * (dens**rx))
    
    # AI, SI, densitas EEI formulation (omitted density)
    elif calcmethod == 3:
        # New calculation method
        # Correct definitions for px, qx, and rx
        px = np.cos(np.deg2rad(sdt)) + np.sin(np.deg2rad(sdt))
        qx = -8 * k * np.sin(np.deg2rad(sdt))
        rx = np.sin(np.deg2rad(sdt)) * (4 * k - 1)

        AI = pvel * dens
        SI = svel * dens

        ao = np.nanmean(pvel)
        bo = np.nanmean(svel)
        co = np.nanmean(dens)
        AIo = np.nanmean(AI)
        SIo = np.nanmean(SI)
        
        scf = (ao * co) / ((AIo**px) * (SIo**qx))
        
        # Using the correct equation: EEI(x) = AI^(cos(x)+sin(x)) ⋅ SI^(−8ksin(x)) ⋅ ρ^(sin(x)(4k−1))
        EEI = scf * ((AI**px) * (SI**qx))
        
    else:
        raise ValueError("Invalid calcmethod. Use 1, 2, or 3.")

    # EGI calculation (unchanged)
    sdt2 = 90
    px2 = np.cos(np.deg2rad(sdt2)) + np.sin(np.deg2rad(sdt2))
    qx2 = -8 * k * np.sin(np.deg2rad(sdt2))
    rx2 = np.cos(np.deg2rad(sdt2)) - 4 * k * np.sin(np.deg2rad(sdt2))
    
    scf2 = (ao * co) / ((ao**px2) * (bo**qx2) * (co**rx2))

    EGI = scf2 * ((pvel**px2) * (svel**qx2) * (dens**rx2))

    scaler = scf

    return EEI, EGI, scaler