# Calculated Curves Test Plan

## Overview
This document provides test cases and verification steps to ensure the calculated curves functionality works correctly after implementing the fixes.

## Test Setup

### Prerequisites
1. Have at least 2 LAS files loaded
2. Ensure the LAS files have common curves like DT, RHOB, NPHI, GR
3. Have the calculator functionality enabled

### Test Data Preparation
```python
# Sample calculations to test
TEST_CALC_1 = "AI = RHOB * (304800/DT)"  # Acoustic Impedance
TEST_CALC_2 = "PHIE_HC = PHIE * (1 - SWE)"  # Hydrocarbon porosity
TEST_CALC_3 = "DT_NORMALIZED = (DT - np.nanmin(DT)) / (np.nanmax(DT) - np.nanmin(DT))"
```

## Test Cases

### Test Case 1: Verify Calculated Curves Metadata
**Objective**: Ensure calculated curves are properly tagged with metadata

**Steps**:
1. Load LAS files
2. Run calculator with test calculations
3. Check console output for:
   ```
   ✅ Added calculated curve 'AI' to well [WELL_NAME]
   🔍 PHASE 1 DEBUG: Calculated curves for [WELL_NAME]: {'AI'}
   ```

**Expected Result**:
- Each calculated curve should have `_calculated_curves` metadata
- Metadata should persist through the workflow

**Verification Code**:
```python
# Add this debug code after calculations
for las in las_files:
    if hasattr(las, '_calculated_curves'):
        print(f"Well {las.well.WELL.value} calculated curves: {las._calculated_curves}")
    else:
        print(f"Well {las.well.WELL.value} has NO calculated curves metadata!")
```

### Test Case 2: Column Categorization
**Objective**: Verify original and calculated columns are properly separated

**Steps**:
1. After calculations, proceed to column selection dialog
2. Check console output for categorization debug messages
3. Verify the dropdown shows two sections:
   - "--- Original Logs ---"
   - "--- Calculated Curves ---"

**Expected Console Output**:
```
=== DEBUG: COLUMN CATEGORIZATION ===
Well WELL_001:
  Total curves: 25
  Calculated curves metadata: {'AI', 'PHIE_HC', 'DT_NORMALIZED'}
  Curve 'AI' identified as calculated
  Curve 'PHIE_HC' identified as calculated
  Curve 'DT_NORMALIZED' identified as calculated

Total original curves: 22
Total calculated curves: 3
```

**Verification Checklist**:
- [ ] Original logs section contains only original curves
- [ ] Calculated curves section contains only calculated curves
- [ ] No curves appear in both sections
- [ ] All calculated curves are marked with 🧮 emoji

### Test Case 3: Dropdown Display
**Objective**: Ensure calculated curves appear in selection dropdowns

**Steps**:
1. Open column selection dialog after calculations
2. Check X-axis dropdown
3. Check Y-axis dropdown
4. Check Z-axis dropdown (if applicable)

**Expected Result**:
- All dropdowns should show calculated curves in a separate section
- Calculated curves should be selectable
- Display format: "🧮 AI (calculated)"

### Test Case 4: Cross-Well Consistency
**Objective**: Verify calculated curves work across multiple wells

**Steps**:
1. Load 3+ LAS files
2. Run calculations that use common curves
3. Check if calculated curves appear for all wells

**Expected Result**:
- If a curve exists in all wells, calculated curve should show as "✅"
- If a curve exists in some wells, calculated curve should show as "⚠️"

**Verification Matrix**:
| Well | DT | RHOB | AI (Calculated) | Status |
|------|----|----|-----------------|---------|
| WELL_001 | ✓ | ✓ | ✓ | ✅ |
| WELL_002 | ✓ | ✓ | ✓ | ✅ |
| WELL_003 | ✓ | ✗ | ✗ | ⚠️ |

### Test Case 5: Plot Creation with Calculated Curves
**Objective**: Verify calculated curves can be plotted

**Steps**:
1. Select a calculated curve for X-axis
2. Select an original curve for Y-axis
3. Create the plot

**Expected Result**:
- Plot should display correctly
- Axis labels should show the curve names
- No errors in console

### Test Case 6: Edge Cases

#### 6.1 Empty Calculation
**Test**: Submit empty calculation text
**Expected**: No new curves added, workflow continues

#### 6.2 Invalid Calculation
**Test**: Submit calculation with syntax error
**Expected**: Error message displayed, option to retry

#### 6.3 Calculation Using Non-Existent Curve
**Test**: `NEW_CURVE = INVALID_CURVE * 2`
**Expected**: Validation error before execution

#### 6.4 Overwriting Existing Curve
**Test**: Calculate a curve with same name as original
**Expected**: Original curve preserved, calculated curve not added

## Debug Verification Points

### 1. After Calculator Execution
Add this code block to verify curves are added:
```python
print("\n=== CALCULATOR VERIFICATION ===")
for las in las_files:
    print(f"\nWell: {las.well.WELL.value}")
    print(f"Total curves: {len(las.curves)}")
    print(f"Curve names: {sorted(las.curves.keys())}")
    if hasattr(las, '_calculated_curves'):
        print(f"Calculated: {las._calculated_curves}")
```

### 2. In Column Selection Dialog
Check for these debug messages:
```
DEBUG: create_column_display_list called
  Original columns (22): ['CALI', 'DT', 'GR', 'NPHI', 'RHOB']...
  Calculated columns (3): ['AI', 'DT_NORMALIZED', 'PHIE_HC']
```

### 3. Before Plot Creation
Verify selected columns:
```
Selected X Column (display): '🧮 AI (calculated)'
Extracted X Column (clean): 'AI'
```

## Common Issues and Solutions

### Issue: Calculated curves not showing in dropdown
**Check**:
1. Are curves actually added to LAS object?
2. Is `_calculated_curves` metadata set?
3. Is column analysis happening after calculations?

**Debug**:
```python
# Check if curve exists
if 'AI' in las.curves:
    print("AI curve exists")
    print(f"AI data length: {len(las['AI'].data)}")
```

### Issue: Calculated curves in wrong section
**Check**:
1. Is curve name in `calculated_columns_set`?
2. Is filtering logic excluding calculated from original?

**Debug**:
```python
print(f"Curve 'AI' in calculated set: {'AI' in calculated_columns}")
print(f"Curve 'AI' in original set: {'AI' in original_columns}")
```

## Automated Test Function

```python
def test_calculated_curves_system():
    """Automated test for calculated curves functionality"""
    
    print("=== CALCULATED CURVES SYSTEM TEST ===")
    
    # Test 1: Metadata persistence
    test_passed = True
    for las in las_files:
        if not hasattr(las, '_calculated_curves'):
            print(f"❌ FAIL: Well {las.well.WELL.value} missing _calculated_curves")
            test_passed = False
    
    if test_passed:
        print("✅ PASS: All wells have calculated curves metadata")
    
    # Test 2: Curve existence
    expected_curves = ['AI', 'PHIE_HC', 'DT_NORMALIZED']
    for las in las_files:
        for curve in expected_curves:
            if curve not in las.curves:
                print(f"❌ FAIL: Curve {curve} not found in {las.well.WELL.value}")
            else:
                print(f"✅ PASS: Curve {curve} found in {las.well.WELL.value}")
    
    # Test 3: Data integrity
    for las in las_files:
        depth_len = len(las['DEPTH'].data)
        for curve in expected_curves:
            if curve in las.curves:
                curve_len = len(las[curve].data)
                if curve_len != depth_len:
                    print(f"❌ FAIL: Length mismatch for {curve} in {las.well.WELL.value}")
                else:
                    print(f"✅ PASS: Correct length for {curve} in {las.well.WELL.value}")
    
    print("=== TEST COMPLETE ===")
```

## Success Criteria

The implementation is successful when:
1. ✅ All calculated curves appear only in "Calculated Curves" section
2. ✅ No calculated curves appear in "Original Logs" section  
3. ✅ Calculated curves are immediately available after calculation
4. ✅ Calculated curves can be selected and plotted
5. ✅ Console shows clear debug output for troubleshooting
6. ✅ Edge cases are handled gracefully

## Test Execution Checklist

- [ ] Run Test Case 1: Metadata verification
- [ ] Run Test Case 2: Column categorization
- [ ] Run Test Case 3: Dropdown display
- [ ] Run Test Case 4: Cross-well consistency
- [ ] Run Test Case 5: Plot creation
- [ ] Run Test Case 6: Edge cases
- [ ] Execute automated test function
- [ ] Verify all success criteria met